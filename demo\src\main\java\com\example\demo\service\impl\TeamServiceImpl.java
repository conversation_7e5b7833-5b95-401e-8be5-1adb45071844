package com.example.demo.service.impl;

import com.example.demo.common.PageResult;
import com.example.demo.dto.TeamCreateRequest;
import com.example.demo.dto.TeamDTO;
import com.example.demo.dto.TeamApplicationDTO;
import com.example.demo.entity.Project;
import com.example.demo.entity.Team;
import com.example.demo.entity.TeamApplication;
import com.example.demo.entity.Record;
import com.example.demo.entity.TeamMember;
import com.example.demo.entity.User;
import com.example.demo.repository.RecordRepository;
import com.example.demo.repository.TeamApplicationRepository;
import com.example.demo.repository.TeamMemberRepository;
import com.example.demo.repository.TeamRepository;
import com.example.demo.repository.ProjectRepository;
import com.example.demo.repository.UserRepository;
import com.example.demo.service.ProjectService;
import com.example.demo.service.TeamService;
import com.example.demo.service.UserService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.Arrays;
import org.springframework.jdbc.core.JdbcTemplate;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 团队服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class TeamServiceImpl implements TeamService {
    
    private final TeamRepository teamRepository;
    private final TeamMemberRepository teamMemberRepository;
    private final TeamApplicationRepository teamApplicationRepository;
    private final ProjectRepository projectRepository;
    private final UserRepository userRepository;
    private final RecordRepository recordRepository;
    private final ProjectService projectService;
    private final UserService userService;
    private final JdbcTemplate jdbcTemplate;
    
    @Override
    @Transactional
    public Team createTeam(Long leaderId, TeamCreateRequest request) {
        log.info("创建团队: {} by user {}", request.getName(), leaderId);

        // 检查用户是否已经是其他团队的成员
        if (hasUserInAnyTeam(leaderId)) {
            throw new RuntimeException("您已经属于其他团队，无法创建新团队");
        }

        // 获取队长信息
        User leader = userService.findById(leaderId);
        if (!User.UserRole.STUDENT.equals(leader.getRole())) {
            throw new RuntimeException("只有学生可以创建团队");
        }

        // 创建团队
        Team team = new Team();
        team.setName(request.getName());
        team.setDescription(request.getDescription());
        team.setMaxMembers(request.getMaxMembers());
        team.setLeaderId(leaderId);
        team.setStatus(Team.TeamStatus.APPROVED); // 团队创建后即可招募成员

        Team savedTeam = teamRepository.save(team);

        // 添加队长作为团队成员
        TeamMember leaderMember = new TeamMember();
        leaderMember.setTeamId(savedTeam.getId());
        leaderMember.setUserId(leaderId);
        leaderMember.setRole(TeamMember.MemberRole.LEADER);
        leaderMember.setStatus(TeamMember.MemberStatus.ACTIVE);
        teamMemberRepository.save(leaderMember);

        log.info("团队创建成功: {}", savedTeam.getName());
        return savedTeam;
    }

    @Override
    @Transactional
    public Team createTeamAndApply(Long leaderId, TeamCreateRequest request) {
        // 暂时使用createTeam方法，后续会分离项目申请功能
        return createTeam(leaderId, request);
    }
    
    @Override
    public Team findById(Long id) {
        return teamRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("团队不存在"));
    }
    
    @Override
    public TeamDTO getTeamDTO(Long teamId) {
        Team team = findById(teamId);

        // 确保队长信息被加载
        if (team.getLeader() == null && team.getLeaderId() != null) {
            User leader = userService.findById(team.getLeaderId());
            team.setLeader(leader);
        }

        // 确保项目信息被加载
        if (team.getProject() != null && team.getProject().getTeacher() == null) {
            // 如果项目的教师信息没有被加载，手动加载
            Project project = projectRepository.findById(team.getProject().getId()).orElse(null);
            if (project != null) {
                team.setProject(project);
            }
        }

        // 创建DTO并手动设置活跃成员
        TeamDTO dto = TeamDTO.fromTeam(team);

        // 获取活跃的团队成员并设置到DTO中
        List<TeamMember> activeMembers = getTeamMembers(teamId);
        if (activeMembers != null) {
            List<TeamDTO.MemberInfo> memberInfos = activeMembers.stream()
                    .map(TeamDTO.MemberInfo::fromTeamMember)
                    .collect(java.util.stream.Collectors.toList());
            dto.setMembers(memberInfos);
            dto.setMemberCount(activeMembers.size());
        }

        return dto;
    }
    
    @Override
    @Transactional
    public Team updateTeam(Long teamId, Long leaderId, String name, String description) {
        log.info("更新团队信息: {} by leader {}", teamId, leaderId);

        Team team = findById(teamId);

        // 检查权限 - 使用leaderId字段而不是懒加载的leader对象
        if (!team.getLeaderId().equals(leaderId)) {
            throw new RuntimeException("只有队长可以修改团队信息");
        }

        // 检查团队状态
        if (Team.TeamStatus.APPROVED.equals(team.getStatus()) ||
            Team.TeamStatus.REJECTED.equals(team.getStatus())) {
            throw new RuntimeException("已审核的团队不能修改信息");
        }

        if (name != null && !name.trim().isEmpty()) {
            team.setName(name.trim());
        }
        if (description != null) {
            team.setDescription(description.trim());
        }

        Team updatedTeam = teamRepository.save(team);
        log.info("团队信息更新成功: {}", updatedTeam.getName());

        return updatedTeam;
    }
    
    @Override
    @Transactional
    public void disbandTeam(Long teamId, Long leaderId) {
        log.info("解散团队: {} by leader {}", teamId, leaderId);

        try {
            // 使用JDBC直接查询团队信息，避免JPA实体加载
            String checkSql = "SELECT leader_id, status, name FROM teams WHERE id = ?";
            List<Map<String, Object>> results = jdbcTemplate.queryForList(checkSql, teamId);

            if (results.isEmpty()) {
                throw new RuntimeException("团队不存在");
            }

            Map<String, Object> teamInfo = results.get(0);
            Long teamLeaderId = ((Number) teamInfo.get("leader_id")).longValue();
            String status = (String) teamInfo.get("status");
            String teamName = (String) teamInfo.get("name");

            // 检查权限
            if (!teamLeaderId.equals(leaderId)) {
                throw new RuntimeException("只有队长可以解散团队");
            }

            // 检查团队状态 - 只有正在工作中的团队不能解散
            if ("WORKING".equals(status)) {
                throw new RuntimeException("正在进行项目的团队不能解散");
            }

            // 使用JDBC直接删除，完全绕过JPA
            // 按照外键依赖顺序删除相关记录

            log.info("开始删除团队相关数据，团队ID: {}", teamId);

            // 1. 删除文件信息记录（解决外键约束问题）
            int fileCount = jdbcTemplate.update("DELETE FROM file_info WHERE team_id = ?", teamId);
            log.info("删除文件记录: {} 条", fileCount);

            // 2. 删除与评价相关的记录（反馈和评价项目）
            int feedbackCount = jdbcTemplate.update(
                "DELETE r FROM records r " +
                "INNER JOIN evaluations e ON r.target_id = e.id " +
                "WHERE r.type IN ('FEEDBACK', 'EVALUATION_ITEM') AND e.team_id = ?", teamId);
            log.info("删除评价相关记录: {} 条", feedbackCount);

            // 3. 删除项目评价记录
            int evalCount = jdbcTemplate.update("DELETE FROM evaluations WHERE team_id = ?", teamId);
            log.info("删除评价记录: {} 条", evalCount);

            // 4. 删除团队相关记录（任务、讨论、反馈等）
            int recordCount = jdbcTemplate.update("DELETE FROM records WHERE team_id = ?", teamId);
            log.info("删除团队记录: {} 条", recordCount);

            // 5. 删除团队成员
            int memberCount = jdbcTemplate.update("DELETE FROM team_members WHERE team_id = ?", teamId);
            log.info("删除团队成员记录: {} 条", memberCount);

            // 6. 删除团队申请记录
            int teamAppCount = jdbcTemplate.update("DELETE FROM team_applications WHERE team_id = ?", teamId);
            log.info("删除团队申请记录: {} 条", teamAppCount);

            // 7. 检查是否还有其他引用
            String constraintSql = "SELECT TABLE_NAME, COLUMN_NAME FROM INFORMATION_SCHEMA.KEY_COLUMN_USAGE WHERE REFERENCED_TABLE_NAME = 'teams' AND REFERENCED_COLUMN_NAME = 'id'";
            List<Map<String, Object>> constraints = jdbcTemplate.queryForList(constraintSql);
            log.info("发现外键约束: {}", constraints);

            // 8. 最后删除团队
            int teamCount = jdbcTemplate.update("DELETE FROM teams WHERE id = ?", teamId);
            log.info("删除团队记录: {} 条", teamCount);

            log.info("团队解散成功: {}", teamName);

        } catch (Exception e) {
            log.error("解散团队失败: {}", e.getMessage(), e);
            throw new RuntimeException("解散团队失败: " + e.getMessage());
        }
    }



    @Override
    @Transactional
    public void reviewTeamApplication(Long teamId, Long teacherId, Team.TeamStatus status, String reason) {
        log.info("审核团队申请: {} -> {} by teacher {}", teamId, status, teacherId);
        
        Team team = findById(teamId);
        
        // 检查权限
        if (!team.getProject().getTeacher().getId().equals(teacherId)) {
            throw new RuntimeException("只能审核自己项目的团队申请");
        }
        
        // 检查团队状态
        if (!Team.TeamStatus.PENDING.equals(team.getStatus())) {
            throw new RuntimeException("只能审核待审核状态的团队");
        }
        
        // 检查审核状态
        if (!Team.TeamStatus.APPROVED.equals(status) &&
            !Team.TeamStatus.WORKING.equals(status) &&
            !Team.TeamStatus.REJECTED.equals(status)) {
            throw new RuntimeException("审核状态只能是通过或拒绝");
        }

        boolean isApproved = Team.TeamStatus.APPROVED.equals(status) || Team.TeamStatus.WORKING.equals(status);

        // 设置审核结果
        if (isApproved) {
            team.setStatus(Team.TeamStatus.WORKING);  // 审核通过直接设为工作中
        } else {
            team.setStatus(Team.TeamStatus.REJECTED);  // 审核拒绝
        }
        team.setTeacherFeedback(reason);
        teamRepository.save(team);

        // 如果通过审核，更新项目的当前团队数量和项目状态
        if (isApproved) {
            projectService.incrementTeamCount(team.getProjectId());

            // 检查是否是第一个通过审核的团队，如果是则将项目状态设为进行中
            long approvedTeamCount = teamRepository.countByProjectIdAndStatus(
                team.getProjectId(), Team.TeamStatus.WORKING);
            if (approvedTeamCount == 1) {
                projectService.updateProjectStatus(team.getProjectId(),
                    team.getProject().getTeacher().getId(),
                    Project.ProjectStatus.IN_PROGRESS);
            }
        }

        log.info("团队申请审核完成: {} -> {}", team.getName(), status);
    }

    @Override
    @Transactional
    public void stopRecruiting(Long teamId, Long leaderId) {
        log.info("停止团队招募 - 团队ID: {}, 队长ID: {}", teamId, leaderId);

        Team team = findById(teamId);
        if (team == null) {
            throw new RuntimeException("团队不存在");
        }

        // 检查权限 - 只有队长可以停止招募
        if (!team.getLeader().getId().equals(leaderId)) {
            throw new RuntimeException("只有队长可以停止招募");
        }

        // 检查团队状态 - 只有招募中的团队可以停止招募
        if (team.getStatus() != Team.TeamStatus.APPROVED) {
            throw new RuntimeException("只有招募中的团队可以停止招募");
        }

        // 更新团队状态为停止招募
        team.setStatus(Team.TeamStatus.RECRUITING_STOPPED);
        teamRepository.save(team);

        log.info("团队停止招募成功 - 团队: {}", team.getName());
    }

    @Override
    public void removeTeamByTeacher(Long teamId, Long teacherId) {
        log.info("=== 教师移除团队开始 - 团队ID: {}, 教师ID: {} ===", teamId, teacherId);

        try {
            // 1. 验证团队和权限（非事务操作）
            TeamRemovalInfo removalInfo = validateTeamRemoval(teamId, teacherId);

            // 2. 更新团队状态（独立事务）
            updateTeamStatus(teamId);

            // 3. 删除团队相关数据（独立事务）
            deleteTeamRecords(teamId);

            // 4. 发送通知（独立事务）
            try {
                sendTeamRemovalNotification(teamId, removalInfo.leaderUserId, teacherId);
            } catch (Exception e) {
                log.error("=== 发送通知失败，但不影响主流程 ===", e);
            }

            log.info("=== 教师移除团队成功 - 团队: {} ===", removalInfo.teamName);
        } catch (Exception e) {
            log.error("=== 教师移除团队失败 ===", e);
            throw e;
        }
    }

    /**
     * 验证团队移除权限（非事务方法）
     */
    private TeamRemovalInfo validateTeamRemoval(Long teamId, Long teacherId) {
        Team team = findById(teamId);
        if (team == null) {
            log.error("=== 团队不存在 - 团队ID: {} ===", teamId);
            throw new RuntimeException("团队不存在");
        }

        // 检查权限：只有项目发布教师可以移除团队
        if (team.getProject() == null && team.getProjectId() == null) {
            log.error("=== 团队未关联项目，无法移除 - 团队ID: {} ===", teamId);
            throw new RuntimeException("团队未参与任何项目，无法移除");
        }

        // 手动加载项目信息，避免懒加载问题
        Project project = team.getProject();
        if (project == null && team.getProjectId() != null) {
            project = projectService.findById(team.getProjectId());
            team.setProject(project);
        }

        // 手动加载教师信息，避免懒加载问题
        if (project.getTeacher() == null && project.getTeacherId() != null) {
            User teacher = userService.findById(project.getTeacherId());
            project.setTeacher(teacher);
        }

        if (!project.getTeacher().getId().equals(teacherId)) {
            log.error("=== 权限不足 - 教师ID: {}, 项目教师ID: {} ===",
                teacherId, project.getTeacher().getId());
            throw new RuntimeException("只能移除参与自己项目的团队");
        }

        // 返回需要的信息
        TeamRemovalInfo info = new TeamRemovalInfo();
        info.teamName = team.getName();
        info.leaderUserId = team.getLeaderId();
        return info;
    }

    /**
     * 更新团队状态（独立事务）
     */
    @Transactional(propagation = org.springframework.transaction.annotation.Propagation.REQUIRES_NEW)
    private void updateTeamStatus(Long teamId) {
        try {
            Team team = teamRepository.findById(teamId).orElse(null);
            if (team == null) {
                throw new RuntimeException("团队不存在");
            }

            team.setProject(null);
            team.setProjectId(null);
            team.setStatus(Team.TeamStatus.RECRUITING_STOPPED);
            teamRepository.save(team);
            log.info("=== 团队项目关联已清空，状态已更新 ===");
        } catch (Exception e) {
            log.error("=== 更新团队状态失败 ===", e);
            throw e;
        }
    }

    /**
     * 删除团队相关数据（独立事务）
     */
    @Transactional(propagation = org.springframework.transaction.annotation.Propagation.REQUIRES_NEW)
    private void deleteTeamRecords(Long teamId) {
        try {
            int deletedRecords = jdbcTemplate.update(
                "DELETE FROM records WHERE team_id = ? AND type IN ('TASK', 'DISCUSSION')",
                teamId);
            log.info("=== 删除团队任务和讨论记录: {} 条 ===", deletedRecords);
        } catch (Exception e) {
            log.error("=== 删除团队相关数据失败 ===", e);
            throw e;
        }
    }

    /**
     * 团队移除信息传递类
     */
    private static class TeamRemovalInfo {
        private String teamName;
        private Long leaderUserId;
    }

    @Override
    @Transactional(readOnly = true)
    public PageResult<TeamDTO> findAllTeams(Pageable pageable) {
        Page<Team> page = teamRepository.findAll(pageable);
        return convertToTeamDTOPage(page);
    }
    
    @Override
    public PageResult<TeamDTO> findTeamsByLeader(Long leaderId, Pageable pageable) {
        log.info("查询用户作为队长的团队 - 用户ID: {}", leaderId);
        Page<Team> page = teamRepository.findByLeaderId(leaderId, pageable);
        log.info("数据库查询结果 - 总数: {}, 当前页: {}", page.getTotalElements(), page.getContent().size());
        return convertToTeamDTOPage(page);
    }
    
    @Override
    public PageResult<TeamDTO> findTeamsByMember(Long memberId, Pageable pageable) {
        log.info("查询用户参与的团队 - 用户ID: {}", memberId);
        System.out.println("=== TeamService: 查询用户参与的团队 - 用户ID: " + memberId + " ===");

        Page<Team> page = teamRepository.findByMemberId(memberId, pageable);
        System.out.println("=== 分页查询结果 - 总数: " + page.getTotalElements() + ", 当前页: " + page.getContent().size() + " ===");

        // 打印查询到的团队信息
        for (Team team : page.getContent()) {
            System.out.println("=== 找到团队: ID=" + team.getId() + ", 名称=" + team.getName() + ", 队长ID=" + team.getLeaderId() + " ===");
        }

        log.info("数据库查询结果 - 总数: {}, 当前页: {}", page.getTotalElements(), page.getContent().size());
        return convertToTeamDTOPage(page);
    }
    
    @Override
    public PageResult<TeamDTO> findTeamsByProject(Long projectId, Pageable pageable) {
        Page<Team> page = teamRepository.findByProjectId(projectId, pageable);
        return convertToTeamDTOPage(page);
    }
    
    @Override
    public PageResult<TeamDTO> findTeamsByStatus(Team.TeamStatus status, Pageable pageable) {
        Page<Team> page = teamRepository.findByStatus(status, pageable);
        return convertToTeamDTOPage(page);
    }
    
    @Override
    public PageResult<TeamDTO> searchTeams(String keyword, Pageable pageable) {
        Page<Team> page = teamRepository.searchTeams(keyword, pageable);
        return convertToTeamDTOPage(page);
    }
    
    @Override
    public Team getCurrentTeamInProject(Long userId, Long projectId) {
        return teamRepository.findByUserIdAndProjectId(userId, projectId).orElse(null);
    }
    
    @Override
    public boolean hasTeamInProject(Long userId, Long projectId) {
        return teamRepository.existsByUserIdAndProjectId(userId, projectId);
    }

    @Override
    public boolean canJoinTeam(Long teamId, Long userId) {
        try {
            Team team = findById(teamId);
            List<TeamMember> members = getTeamMembers(teamId);
            int maxSize = team.getProject() != null ?
                         team.getProject().getMaxTeamSize() :
                         team.getMaxMembers();

            return Team.TeamStatus.APPROVED.equals(team.getStatus()) &&
                   !isTeamMember(teamId, userId) &&
                   members.size() < maxSize;
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public boolean isTeamLeader(Long teamId, Long userId) {
        try {
            Team team = findById(teamId);
            return team.getLeaderId().equals(userId);
        } catch (Exception e) {
            return false;
        }
    }

    @Override
    public boolean isTeamMember(Long teamId, Long userId) {
        return teamMemberRepository.existsByTeamIdAndUserId(teamId, userId) ||
               isTeamLeader(teamId, userId);
    }
    
    @Override
    public List<TeamMember> getTeamMembers(Long teamId) {
        // 只返回活跃的团队成员
        List<TeamMember> members = teamMemberRepository.findByTeamIdAndStatus(teamId, TeamMember.MemberStatus.ACTIVE);

        // 手动加载User信息，避免懒加载问题
        for (TeamMember member : members) {
            if (member.getUser() == null && member.getUserId() != null) {
                try {
                    User user = userService.findById(member.getUserId());
                    member.setUser(user);
                } catch (Exception e) {
                    log.warn("无法加载用户信息，用户ID: {}", member.getUserId(), e);
                }
            }
        }

        return members;
    }
    
    @Override
    public long countTeams() {
        return teamRepository.count();
    }
    
    @Override
    public List<Object[]> countTeamsByStatus() {
        return teamRepository.countTeamsByStatus();
    }

    @Override
    public long countTeamsByProjects(List<Long> projectIds) {
        if (projectIds == null || projectIds.isEmpty()) {
            return 0;
        }
        return teamRepository.countByProjectIdIn(projectIds);
    }

    @Override
    public List<Object[]> countTeamsByStatusAndProjects(List<Long> projectIds) {
        if (projectIds == null || projectIds.isEmpty()) {
            return new ArrayList<>();
        }
        return teamRepository.countTeamsByStatusAndProjects(projectIds);
    }

    @Override
    public Long getTeamIdByMember(Long userId) {
        // 先查找用户是否是队长
        List<Team> teams = teamRepository.findByLeaderId(userId);
        if (!teams.isEmpty()) {
            return teams.get(0).getId();
        }

        // 再查找用户是否是成员
        Team team = teamRepository.findByMemberId(userId);
        return team != null ? team.getId() : null;
    }

    @Override
    public Long getProjectIdByTeam(Long teamId) {
        Team team = findById(teamId);
        return team.getProjectId();
    }

    @Override
    public Object[] getUserTeamStats(Long userId) {
        return teamRepository.getUserTeamStats(userId);
    }
    
    /**
     * 转换Team分页为TeamDTO分页
     */
    private PageResult<TeamDTO> convertToTeamDTOPage(Page<Team> page) {
        List<TeamDTO> dtoList = page.getContent().stream()
                .map(team -> {
                    // 手动加载队长信息
                    if (team.getLeader() == null && team.getLeaderId() != null) {
                        User leader = userService.findById(team.getLeaderId());
                        team.setLeader(leader);
                    }

                    TeamDTO dto = TeamDTO.fromTeam(team);
                    // 实时计算活跃成员数量
                    List<TeamMember> activeMembers = getTeamMembers(team.getId());
                    dto.setMemberCount(activeMembers.size());
                    return dto;
                })
                .collect(Collectors.toList());

        return PageResult.of(dtoList, page.getTotalElements(),
                           page.getNumber() + 1, page.getSize());
    }
    
    @Override
    @Transactional
    public void applyToJoinTeam(Long teamId, Long userId, String reason) {
        log.info("=== 用户申请加入团队开始 - 团队ID: {}, 用户ID: {} ===", teamId, userId);

        // 检查用户是否已经属于其他团队
        log.info("=== 检查用户是否已属于其他团队 ===");
        if (hasUserInAnyTeam(userId)) {
            log.error("=== 用户申请失败：已属于其他团队 - 用户ID: {} ===", userId);
            throw new RuntimeException("用户已经属于其他团队，无法申请加入新团队");
        }
        log.info("=== 用户未属于其他团队，检查通过 ===");

        // 检查团队是否存在
        log.info("=== 检查团队是否存在 - 团队ID: {} ===", teamId);
        Team team = findById(teamId);
        if (team == null) {
            log.error("=== 团队不存在 - 团队ID: {} ===", teamId);
            throw new RuntimeException("团队不存在");
        }
        log.info("=== 团队存在，团队名称: {}, 当前状态: {} ===", team.getName(), team.getStatus());

        // 检查团队状态是否允许申请加入
        log.info("=== 检查团队状态是否允许申请加入 - 当前状态: {} ===", team.getStatus());
        if (team.getStatus() != Team.TeamStatus.APPROVED) {
            log.error("=== 团队状态不允许申请加入 - 当前状态: {}, 需要状态: APPROVED ===", team.getStatus());
            if (team.getStatus() == Team.TeamStatus.RECRUITING_STOPPED) {
                throw new RuntimeException("该团队已停止招募，无法申请加入");
            } else {
                throw new RuntimeException("该团队当前状态不允许申请加入，只有招募中的团队才能申请加入");
            }
        }
        log.info("=== 团队状态检查通过 ===");

        // 检查团队是否还有空位
        List<TeamMember> currentMembers = getTeamMembers(teamId);
        int maxTeamSize;

        // 如果团队已经绑定项目，使用项目的最大团队大小
        if (team.getProject() != null) {
            maxTeamSize = team.getProject().getMaxTeamSize();
        }
        // 如果团队还没有绑定项目，使用团队自身的最大成员数
        else if (team.getMaxMembers() != null) {
            maxTeamSize = team.getMaxMembers();
        }
        // 默认最大成员数
        else {
            maxTeamSize = 5;
        }

        if (currentMembers.size() >= maxTeamSize) {
            throw new RuntimeException("团队已满，无法加入");
        }

        // 检查用户是否已经是团队成员
        if (isTeamMember(teamId, userId)) {
            throw new RuntimeException("用户已经是团队成员");
        }

        // 检查是否已经有待审核的申请
        if (teamApplicationRepository.existsByUserIdAndTeamIdAndStatus(
                userId, teamId, TeamApplication.ApplicationStatus.PENDING)) {
            throw new RuntimeException("您已经申请过该团队，请等待审核结果");
        }

        // 检查是否有已通过的申请（用户已经是成员）
        if (teamApplicationRepository.existsByUserIdAndTeamIdAndStatus(
                userId, teamId, TeamApplication.ApplicationStatus.APPROVED)) {
            throw new RuntimeException("您已经是该团队成员");
        }

        // 如果之前有被拒绝或取消的申请，删除旧记录以允许重新申请
        List<TeamApplication> oldApplications = teamApplicationRepository.findByUserIdAndTeamIdAndStatusIn(
                userId, teamId, Arrays.asList(
                        TeamApplication.ApplicationStatus.REJECTED,
                        TeamApplication.ApplicationStatus.CANCELLED
                ));
        if (!oldApplications.isEmpty()) {
            log.info("删除用户的旧申请记录，允许重新申请 - 用户ID: {}, 团队ID: {}, 旧记录数: {}",
                    userId, teamId, oldApplications.size());
            teamApplicationRepository.deleteAll(oldApplications);
        }

        // 创建申请记录
        User user = userService.findById(userId);
        TeamApplication application = new TeamApplication();
        application.setUserId(userId);
        application.setTeamId(teamId);
        application.setApplicationMessage(reason);
        application.setStatus(TeamApplication.ApplicationStatus.PENDING);

        teamApplicationRepository.save(application);
        log.info("用户申请加入团队成功 - 团队: {}, 用户: {}", team.getName(), user.getUsername());
    }

    @Override
    @Transactional
    public void applyProjectForExistingTeam(Long teamId, Long projectId, Long userId, String reason) {
        log.info("已有团队申请项目 - 团队ID: {}, 项目ID: {}, 用户ID: {}", teamId, projectId, userId);

        // 检查团队是否存在
        Team team = findById(teamId);
        if (team == null) {
            throw new RuntimeException("团队不存在");
        }

        // 检查用户是否是队长
        if (!team.getLeader().getId().equals(userId)) {
            throw new RuntimeException("只有团队队长才能申请项目");
        }

        // 检查项目是否可以申请
        if (!projectService.canApplyProject(projectId)) {
            throw new RuntimeException("该项目当前不可申请");
        }

        // 检查团队状态是否允许申请项目
        if (team.getStatus() != Team.TeamStatus.RECRUITING_STOPPED &&
            team.getStatus() != Team.TeamStatus.APPROVED &&
            team.getStatus() != Team.TeamStatus.COMPLETED) {
            throw new RuntimeException("只有停止招募、已批准或已完成项目的团队才能申请项目");
        }

        // 检查团队是否已经申请了活跃的项目（排除已完成的项目）
        log.info("检查团队项目关联 - 团队ID: {}, project_id字段: {}, project对象: {}",
                 teamId, team.getProjectId(), team.getProject());

        // 优先检查project_id字段，避免懒加载问题
        if (team.getProjectId() != null) {
            log.info("团队有project_id关联: {}", team.getProjectId());
            Project currentProject = projectService.findById(team.getProjectId());
            log.info("关联的项目状态: {}", currentProject.getStatus());

            // 如果当前项目未完成，则不能申请新项目
            if (currentProject.getStatus() != Project.ProjectStatus.COMPLETED &&
                currentProject.getStatus() != Project.ProjectStatus.CANCELLED) {
                log.error("团队申请失败 - 当前项目状态: {}, 项目名称: {}",
                         currentProject.getStatus(), currentProject.getName());
                throw new RuntimeException("团队已经申请了其他项目，不能重复申请");
            }
            // 如果当前项目已完成或取消，清除关联以便申请新项目
            log.info("清除团队与已完成/取消项目的关联 - 项目状态: {}", currentProject.getStatus());
            team.setProject(null);
            team.setProjectId(null);
            teamRepository.save(team);
            log.info("清除团队 [{}] 与已完成项目的关联，允许申请新项目", team.getName());
        } else {
            log.info("团队没有项目关联，可以申请新项目");
        }

        // 获取项目信息并检查容量
        Project project = projectService.findById(projectId);

        // 检查项目团队容量
        long currentTeamCount = teamRepository.countByProjectId(projectId);
        if (currentTeamCount >= project.getMaxTeams()) {
            throw new RuntimeException("项目团队数量已满，无法申请");
        }

        // 检查团队人数是否符合项目要求
        List<TeamMember> members = getTeamMembers(teamId);
        if (members.size() > project.getMaxTeamSize()) {
            throw new RuntimeException("团队人数超过项目要求的最大团队规模");
        }

        // 更新团队的项目信息，设置为待审核状态（需要教师审核）
        team.setProject(project);
        team.setProjectId(projectId);
        team.setStatus(Team.TeamStatus.PENDING);  // 修改为待审核状态
        team.setApplicationMessage(reason);

        teamRepository.save(team);

        // 注意：不立即增加项目团队数量，等审核通过后再增加
        // 注意：不立即修改项目状态，等有团队审核通过后再修改

        log.info("团队申请项目成功 - 团队: {}, 项目: {}", team.getName(), project.getName());
    }

    @Override
    @Transactional
    public void leaveTeam(Long teamId, Long memberId) {
        log.info("用户退出团队 - 团队ID: {}, 用户ID: {}", teamId, memberId);

        Team team = findById(teamId);
        if (team == null) {
            throw new RuntimeException("团队不存在");
        }

        // 获取当前团队成员数量
        List<TeamMember> currentMembers = getTeamMembers(teamId);
        boolean isLeader = team.getLeader().getId().equals(memberId);

        // 如果是队长且团队只有一个人，则自动解散团队
        if (isLeader && currentMembers.size() == 1) {
            log.info("队长退出且团队只剩一人，自动解散团队 - 团队ID: {}", teamId);
            disbandTeam(teamId, memberId);
            return;
        }

        // 如果是队长但团队还有其他成员，不允许直接退出
        if (isLeader && currentMembers.size() > 1) {
            throw new RuntimeException("团队还有其他成员，队长不能直接退出团队，请先转让队长权限");
        }

        // 普通成员退出团队（软删除）
        teamMemberRepository.removeTeamMember(teamId, memberId);
        log.info("用户成功退出团队");
    }

    @Override
    public TeamDTO findTeamByUserId(Long userId) {
        try {
            log.info("查找用户所属团队 - 用户ID: {}", userId);
            System.out.println("=== 查找用户所属团队 - 用户ID: " + userId + " ===");

            // 先查找用户作为队长的团队
            System.out.println("=== 开始查询用户作为队长的团队 ===");
            List<Team> leaderTeams = teamRepository.findByLeaderId(userId);
            System.out.println("=== 队长查询结果数量: " + leaderTeams.size() + " ===");

            for (Team team : leaderTeams) {
                System.out.println("=== 队长团队详情: ID=" + team.getId() + ", 名称=" + team.getName() + ", 状态=" + team.getStatus() + " ===");
            }

            if (!leaderTeams.isEmpty()) {
                Team team = leaderTeams.get(0);
                System.out.println("=== 找到用户作为队长的团队: " + team.getName() + " ===");
                TeamDTO result = getTeamDTO(team.getId());
                System.out.println("=== 转换后的TeamDTO: " + (result != null ? result.getName() : "null") + " ===");
                return result;
            }

            // 再查找用户作为成员的团队（只查找活跃成员）
            System.out.println("=== 开始查询用户作为成员的团队（仅活跃状态）===");
            List<TeamMember> activeMemberRecords = teamMemberRepository.findByUserIdAndStatus(userId, TeamMember.MemberStatus.ACTIVE);
            System.out.println("=== 活跃成员记录数量: " + activeMemberRecords.size() + " ===");

            if (!activeMemberRecords.isEmpty()) {
                TeamMember member = activeMemberRecords.get(0);
                System.out.println("=== 找到用户作为活跃成员的团队: " + member.getTeamId() + " ===");
                return getTeamDTO(member.getTeamId());
            }

            System.out.println("=== 用户没有找到任何活跃团队，返回null表示未加入团队 ===");
            return null;
        } catch (Exception e) {
            log.error("查找用户团队时发生异常 - 用户ID: {}, 错误: {}", userId, e.getMessage(), e);
            System.out.println("=== 查找用户团队时发生异常: " + e.getMessage() + " ===");
            throw new RuntimeException("查找用户团队失败: " + e.getMessage(), e);
        }
    }

    /**
     * 检查用户是否已经属于任何团队（只检查活跃状态）
     */
    private boolean hasUserInAnyTeam(Long userId) {
        log.info("=== 检查用户是否属于任何团队 - 用户ID: {} ===", userId);

        // 检查是否是活跃团队的队长（排除已解散或已完成的团队）
        List<Team> leaderTeams = teamRepository.findByLeaderId(userId);
        log.info("=== 用户作为队长的团队数量: {} ===", leaderTeams.size());

        for (Team team : leaderTeams) {
            // 只检查活跃状态的团队
            if (team.getStatus() == Team.TeamStatus.FORMING ||
                team.getStatus() == Team.TeamStatus.APPLIED ||
                team.getStatus() == Team.TeamStatus.APPROVED ||
                team.getStatus() == Team.TeamStatus.WORKING) {
                log.info("=== 用户是活跃团队的队长，团队状态: {} ===", team.getStatus());
                return true;
            }
        }

        // 检查是否是活跃成员（只检查ACTIVE状态）
        List<TeamMember> activeMemberRecords = teamMemberRepository.findByUserIdAndStatus(userId, TeamMember.MemberStatus.ACTIVE);
        log.info("=== 用户作为活跃成员的团队数量: {} ===", activeMemberRecords.size());

        boolean hasActiveTeam = !activeMemberRecords.isEmpty();
        log.info("=== 用户是否属于任何团队: {} ===", hasActiveTeam);
        return hasActiveTeam;
    }

    @Override
    @Transactional
    public void reviewJoinApplication(Long applicationId, Long reviewerId, boolean approved, String reviewReason) {
        log.info("审核团队加入申请 - 申请ID: {}, 审核人: {}, 结果: {}", applicationId, reviewerId, approved);

        TeamApplication application = teamApplicationRepository.findById(applicationId)
                .orElseThrow(() -> new RuntimeException("申请不存在"));

        // 检查审核人是否是队长
        if (!application.getTeam().getLeader().getId().equals(reviewerId)) {
            throw new RuntimeException("只有队长可以审核团队申请");
        }

        // 检查申请状态
        if (application.getStatus() != TeamApplication.ApplicationStatus.PENDING) {
            throw new RuntimeException("申请已经被处理过了");
        }

        User reviewer = userService.findById(reviewerId);
        application.setResponderId(reviewerId);
        application.setResponseMessage(reviewReason);
        application.setResponseTime(java.time.LocalDateTime.now());

        if (approved) {
            // 再次检查团队是否还有空位
            List<TeamMember> currentMembers = getTeamMembers(application.getTeamId());

            // 获取团队最大成员数（优先级：项目限制 > 团队设置 > 默认值）
            Team team = application.getTeam();
            int maxTeamSize;
            if (team.getProject() != null) {
                maxTeamSize = team.getProject().getMaxTeamSize();
            }
            else if (team.getMaxMembers() != null) {
                maxTeamSize = team.getMaxMembers();
            }
            else {
                maxTeamSize = 5; // 默认最大成员数
            }

            if (currentMembers.size() >= maxTeamSize) {
                throw new RuntimeException("团队已满，无法通过申请");
            }

            // 检查申请人是否已经加入其他团队
            if (hasUserInAnyTeam(application.getUserId())) {
                throw new RuntimeException("申请人已经加入其他团队");
            }

            application.setStatus(TeamApplication.ApplicationStatus.APPROVED);

            // 检查是否已存在团队成员记录（可能是之前退出的）
            Optional<TeamMember> existingMember = teamMemberRepository.findByTeamIdAndUserId(
                    application.getTeamId(), application.getUserId());

            if (existingMember.isPresent()) {
                // 如果存在记录，更新状态为ACTIVE并更新加入时间
                TeamMember member = existingMember.get();
                member.setStatus(TeamMember.MemberStatus.ACTIVE);
                member.setRole(TeamMember.MemberRole.MEMBER);
                member.setJoinTime(java.time.LocalDateTime.now());
                teamMemberRepository.save(member);
                log.info("申请通过，重新激活用户的团队成员记录");
            } else {
                // 如果不存在记录，创建新的团队成员记录
                TeamMember teamMember = new TeamMember();
                teamMember.setTeamId(application.getTeamId());
                teamMember.setUserId(application.getUserId());
                teamMember.setRole(TeamMember.MemberRole.MEMBER);
                teamMember.setStatus(TeamMember.MemberStatus.ACTIVE);
                teamMember.setJoinTime(java.time.LocalDateTime.now());
                teamMemberRepository.save(teamMember);
                log.info("申请通过，创建新的团队成员记录");
            }
        } else {
            application.setStatus(TeamApplication.ApplicationStatus.REJECTED);
            log.info("申请被拒绝");
        }

        teamApplicationRepository.save(application);
    }

    @Override
    public PageResult<TeamApplicationDTO> getTeamApplications(Long teamId, Pageable pageable) {
        Page<TeamApplication> page = teamApplicationRepository.findByTeamIdOrderByApplyTimeDesc(teamId, pageable);
        List<TeamApplicationDTO> dtoList = page.getContent().stream()
                .map(TeamApplicationDTO::fromEntity)
                .collect(Collectors.toList());

        return PageResult.of(dtoList, page.getTotalElements(), page.getNumber() + 1, page.getSize());
    }

    @Override
    public PageResult<TeamApplicationDTO> getPendingApplicationsForLeader(Long leaderId, Pageable pageable) {
        Page<TeamApplication> page = teamApplicationRepository.findByTeamLeaderIdAndStatus(
                leaderId, TeamApplication.ApplicationStatus.PENDING, pageable);

        List<TeamApplicationDTO> dtoList = page.getContent().stream()
                .map(application -> {
                    // 手动加载用户信息，避免懒加载问题
                    if (application.getUser() == null && application.getUserId() != null) {
                        try {
                            User user = userService.findById(application.getUserId());
                            application.setUser(user);
                        } catch (Exception e) {
                            log.warn("无法加载申请人信息，用户ID: {}", application.getUserId(), e);
                        }
                    }

                    // 手动加载团队信息，避免懒加载问题
                    if (application.getTeam() == null && application.getTeamId() != null) {
                        try {
                            Team team = findById(application.getTeamId());
                            application.setTeam(team);
                        } catch (Exception e) {
                            log.warn("无法加载团队信息，团队ID: {}", application.getTeamId(), e);
                        }
                    }

                    return TeamApplicationDTO.fromEntity(application);
                })
                .collect(Collectors.toList());

        return PageResult.of(dtoList, page.getTotalElements(), page.getNumber() + 1, page.getSize());
    }

    @Override
    @Transactional
    public void cancelApplication(Long applicationId, Long applicantId) {
        log.info("=== 取消申请开始 - 申请ID: {}, 申请人ID: {} ===", applicationId, applicantId);

        TeamApplication application = teamApplicationRepository.findById(applicationId)
                .orElseThrow(() -> new RuntimeException("申请不存在"));

        log.info("=== 申请信息 - 申请人ID: {}, 当前状态: {} ===", application.getUserId(), application.getStatus());

        if (!application.getUserId().equals(applicantId)) {
            log.error("=== 权限验证失败 - 申请人ID: {}, 当前用户ID: {} ===", application.getUserId(), applicantId);
            throw new RuntimeException("只能取消自己的申请");
        }

        if (application.getStatus() != TeamApplication.ApplicationStatus.PENDING) {
            log.error("=== 状态验证失败 - 当前状态: {}, 期望状态: PENDING ===", application.getStatus());
            throw new RuntimeException("只能取消待审核的申请");
        }

        // 直接删除申请记录，这样用户可以重新申请
        teamApplicationRepository.delete(application);

        log.info("=== 取消申请成功，已删除申请记录 - 申请ID: {} ===", applicationId);
    }

    @Override
    public TeamApplicationDTO getMyPendingApplication(Long userId) {
        // 查找用户的待处理申请
        List<TeamApplication> applications = teamApplicationRepository.findByUserIdAndStatus(
                userId, TeamApplication.ApplicationStatus.PENDING);

        if (applications.isEmpty()) {
            return null;
        }

        // 返回最新的申请
        TeamApplication latestApplication = applications.get(0);

        // 手动加载用户信息，避免懒加载问题
        if (latestApplication.getUser() == null && latestApplication.getUserId() != null) {
            try {
                User user = userService.findById(latestApplication.getUserId());
                latestApplication.setUser(user);
            } catch (Exception e) {
                log.warn("无法加载申请人信息，用户ID: {}", latestApplication.getUserId(), e);
            }
        }

        // 手动加载团队信息，避免懒加载问题
        if (latestApplication.getTeam() == null && latestApplication.getTeamId() != null) {
            try {
                Team team = findById(latestApplication.getTeamId());
                latestApplication.setTeam(team);
            } catch (Exception e) {
                log.warn("无法加载团队信息，团队ID: {}", latestApplication.getTeamId(), e);
            }
        }

        return TeamApplicationDTO.fromEntity(latestApplication);
    }

    // 其他方法实现省略，需要时可以继续添加
    @Override
    @Transactional
    public void addTeamMember(Long teamId, Long leaderId, Long memberId) {
        // 实现添加团队成员逻辑
        throw new RuntimeException("功能待实现");
    }

    @Override
    @Transactional
    public void removeTeamMember(Long teamId, Long leaderId, Long memberId) {
        log.info("=== 队长移除团队成员开始 - 团队ID: {}, 队长ID: {}, 成员ID: {} ===", teamId, leaderId, memberId);

        try {
            Team team = findById(teamId);
            if (team == null) {
                log.error("=== 团队不存在 - 团队ID: {} ===", teamId);
                throw new RuntimeException("团队不存在");
            }

            // 手动加载队长信息，避免懒加载问题
            if (team.getLeader() == null && team.getLeaderId() != null) {
                User leader = userService.findById(team.getLeaderId());
                team.setLeader(leader);
                log.info("=== 手动加载队长信息成功 ===");
            }

            log.info("=== 团队信息获取成功 - 团队名称: {}, 队长ID: {} ===", team.getName(), team.getLeaderId());

            // 检查权限：只有队长可以移除成员
            if (team.getLeader() == null) {
                log.error("=== 团队队长信息为空 - 团队ID: {} ===", teamId);
                throw new RuntimeException("团队队长信息异常");
            }

            if (!team.getLeader().getId().equals(leaderId)) {
                log.error("=== 权限检查失败 - 当前用户: {}, 队长ID: {} ===", leaderId, team.getLeader().getId());
                throw new RuntimeException("只有队长可以移除团队成员");
            }
            log.info("=== 权限检查通过 ===");

            // 不能移除自己（队长）
            if (leaderId.equals(memberId)) {
                log.error("=== 队长不能移除自己 ===");
                throw new RuntimeException("队长不能移除自己，请先转让队长权限");
            }
            log.info("=== 自我移除检查通过 ===");

            // 检查被移除的用户是否是团队成员
            boolean isMember = isTeamMember(teamId, memberId);
            log.info("=== 成员检查结果 - 用户ID: {}, 是否为成员: {} ===", memberId, isMember);
            if (!isMember) {
                log.error("=== 用户不是团队成员 - 用户ID: {} ===", memberId);
                throw new RuntimeException("该用户不是团队成员");
            }

            // 移除团队成员记录（软删除）
            log.info("=== 开始执行软删除操作 ===");
            teamMemberRepository.removeTeamMember(teamId, memberId);
            log.info("=== 队长成功移除团队成员 ===");
        } catch (Exception e) {
            log.error("=== 移除团队成员失败 ===", e);
            throw e;
        }
    }
    
    @Override
    @Transactional
    public void transferLeadership(Long teamId, Long currentLeaderId, Long newLeaderId) {
        // 实现转让队长逻辑
        throw new RuntimeException("功能待实现");
    }

    @Override
    public List<TeamDTO> getPendingApplicationsByTeacher(Long teacherId) {
        log.info("获取教师的待审核团队申请 - 教师ID: {}", teacherId);

        try {
            // 直接查询所有待审核的团队，然后过滤出属于该教师项目的团队
            List<Team> allPendingTeams = teamRepository.findByStatus(Team.TeamStatus.PENDING);
            log.info("找到所有待审核团队数量: {}", allPendingTeams.size());

            List<TeamDTO> result = new ArrayList<>();
            for (Team team : allPendingTeams) {
                try {
                    // 检查团队是否属于该教师的项目
                    if (team.getProjectId() != null) {
                        Project project = projectService.findById(team.getProjectId());
                        if (project.getTeacherId().equals(teacherId)) {
                            result.add(convertToTeamDTO(team));
                        }
                    }
                } catch (Exception e) {
                    log.warn("处理团队 {} 时出错: {}", team.getId(), e.getMessage());
                }
            }

            log.info("找到教师 {} 的待审核团队数量: {}", teacherId, result.size());
            return result;
        } catch (Exception e) {
            log.error("获取教师待审核申请失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    @Override
    public List<TeamDTO> getReviewedApplicationsByTeacher(Long teacherId) {
        log.info("获取教师的已审核团队申请 - 教师ID: {}", teacherId);

        try {
            // 直接查询所有已审核的团队，然后过滤出属于该教师项目的团队
            List<Team> allReviewedTeams = teamRepository.findByStatusIn(
                    Arrays.asList(Team.TeamStatus.WORKING, Team.TeamStatus.REJECTED)
            );
            log.info("找到所有已审核团队数量: {}", allReviewedTeams.size());

            List<TeamDTO> result = new ArrayList<>();
            for (Team team : allReviewedTeams) {
                try {
                    // 检查团队是否属于该教师的项目
                    if (team.getProjectId() != null) {
                        Project project = projectService.findById(team.getProjectId());
                        if (project.getTeacherId().equals(teacherId)) {
                            result.add(convertToTeamDTO(team));
                        }
                    }
                } catch (Exception e) {
                    log.warn("处理团队 {} 时出错: {}", team.getId(), e.getMessage());
                }
            }

            log.info("找到教师 {} 的已审核团队数量: {}", teacherId, result.size());
            return result;
        } catch (Exception e) {
            log.error("获取教师已审核申请失败: {}", e.getMessage(), e);
            return new ArrayList<>();
        }
    }

    /**
     * 转换Team为TeamDTO
     */
    private TeamDTO convertToTeamDTO(Team team) {
        try {
            // 手动加载队长信息
            if (team.getLeader() == null && team.getLeaderId() != null) {
                User leader = userService.findById(team.getLeaderId());
                team.setLeader(leader);
            }

            // 手动加载项目信息（避免懒加载异常）
            if (team.getProject() == null && team.getProjectId() != null) {
                Project project = projectService.findById(team.getProjectId());
                team.setProject(project);
            }

            TeamDTO dto = TeamDTO.fromTeam(team);

            // 实时计算活跃成员数量
            List<TeamMember> activeMembers = getTeamMembers(team.getId());
            dto.setMemberCount(activeMembers.size());

            // 设置成员信息
            if (activeMembers != null) {
                List<TeamDTO.MemberInfo> memberInfos = activeMembers.stream()
                        .map(TeamDTO.MemberInfo::fromTeamMember)
                        .collect(Collectors.toList());
                dto.setMembers(memberInfos);
            }

            return dto;
        } catch (Exception e) {
            log.error("转换TeamDTO失败 - 团队ID: {}, 错误: {}", team.getId(), e.getMessage(), e);
            throw new RuntimeException("转换团队数据失败: " + e.getMessage());
        }
    }

    /**
     * 发送团队移除通知（独立事务）
     */
    @Transactional(propagation = org.springframework.transaction.annotation.Propagation.REQUIRES_NEW)
    private void sendTeamRemovalNotification(Long teamId, Long leaderId, Long teacherId) {
        try {
            String message = "您的团队已被教师踢出项目";

            // 创建通知记录
            Record notification = new Record();
            notification.setType(Record.RecordType.ANNOUNCEMENT);
            notification.setTitle("团队被移除通知");
            notification.setContent(message);
            notification.setTeamId(teamId);
            notification.setUserId(teacherId); // 发送者是教师
            notification.setStatus(Record.RecordStatus.ACTIVE);
            notification.setPriority(Record.Priority.HIGH);
            notification.setCreateTime(java.time.LocalDateTime.now());

            // 设置接收者为团队队长
            String extraData = String.format("{\"receiverId\":%d,\"notificationType\":\"TEAM\"}", leaderId);
            notification.setExtraData(extraData);

            // 保存通知记录
            recordRepository.save(notification);
            log.info("=== 成功发送通知给队长 - 队长ID: {} ===", leaderId);
        } catch (Exception e) {
            log.error("=== 发送通知失败 ===", e);
            throw e; // 在独立事务中，可以抛出异常
        }
    }

    // ========== 管理员专用方法实现 ==========

    @Override
    public PageResult<Team> findAllTeamEntities(Pageable pageable) {
        log.info("管理员获取所有团队实体 - 页码: {}, 大小: {}", pageable.getPageNumber(), pageable.getPageSize());

        Page<Team> page = teamRepository.findAll(pageable);
        List<Team> teams = page.getContent();

        return PageResult.of(teams, page.getTotalElements(),
                           page.getNumber() + 1, page.getSize());
    }

    @Override
    public PageResult<Team> findTeamEntitiesByStatus(Team.TeamStatus status, Pageable pageable) {
        log.info("管理员按状态获取团队实体 - 状态: {}, 页码: {}, 大小: {}",
                status, pageable.getPageNumber(), pageable.getPageSize());

        Page<Team> page = teamRepository.findByStatus(status, pageable);
        List<Team> teams = page.getContent();

        return PageResult.of(teams, page.getTotalElements(),
                           page.getNumber() + 1, page.getSize());
    }

    @Override
    public PageResult<Team> searchAllTeamEntities(String keyword, Pageable pageable) {
        log.info("管理员搜索团队实体 - 关键词: {}, 页码: {}, 大小: {}",
                keyword, pageable.getPageNumber(), pageable.getPageSize());

        // 暂时使用findAll，后续可以优化搜索功能
        Page<Team> page = teamRepository.findAll(pageable);
        List<Team> teams = page.getContent();

        return PageResult.of(teams, page.getTotalElements(),
                           page.getNumber() + 1, page.getSize());
    }

    @Override
    public PageResult<TeamDTO> findAllTeamDTOs(Pageable pageable) {
        log.info("管理员获取所有团队DTO - 页码: {}, 大小: {}", pageable.getPageNumber(), pageable.getPageSize());

        Page<Team> page = teamRepository.findAll(pageable);
        List<TeamDTO> teamDTOs = page.getContent().stream()
                .map(team -> {
                    // 手动加载关联数据避免懒加载问题
                    if (team.getLeader() == null && team.getLeaderId() != null) {
                        User leader = userService.findById(team.getLeaderId());
                        team.setLeader(leader);
                    }

                    if (team.getProject() == null && team.getProjectId() != null) {
                        Project project = projectService.findById(team.getProjectId());
                        team.setProject(project);
                    }

                    // 计算成员数量
                    Integer memberCount = teamMemberRepository.countByTeamId(team.getId());
                    team.setCurrentMembers(memberCount);

                    return TeamDTO.fromTeam(team);
                })
                .collect(Collectors.toList());

        return PageResult.of(teamDTOs, page.getTotalElements(),
                           page.getNumber() + 1, page.getSize());
    }

    @Override
    public PageResult<TeamDTO> findTeamDTOsByStatus(Team.TeamStatus status, Pageable pageable) {
        log.info("管理员按状态获取团队DTO - 状态: {}, 页码: {}, 大小: {}",
                status, pageable.getPageNumber(), pageable.getPageSize());

        Page<Team> page = teamRepository.findByStatus(status, pageable);
        List<TeamDTO> teamDTOs = page.getContent().stream()
                .map(team -> {
                    // 手动加载关联数据避免懒加载问题
                    if (team.getLeader() == null && team.getLeaderId() != null) {
                        User leader = userService.findById(team.getLeaderId());
                        team.setLeader(leader);
                    }

                    if (team.getProject() == null && team.getProjectId() != null) {
                        Project project = projectService.findById(team.getProjectId());
                        team.setProject(project);
                    }

                    // 计算成员数量
                    Integer memberCount = teamMemberRepository.countByTeamId(team.getId());
                    team.setCurrentMembers(memberCount);

                    return TeamDTO.fromTeam(team);
                })
                .collect(Collectors.toList());

        return PageResult.of(teamDTOs, page.getTotalElements(),
                           page.getNumber() + 1, page.getSize());
    }

    @Override
    public PageResult<TeamDTO> searchAllTeamDTOs(String keyword, Pageable pageable) {
        log.info("管理员搜索团队DTO - 关键词: {}, 页码: {}, 大小: {}",
                keyword, pageable.getPageNumber(), pageable.getPageSize());

        // 暂时使用findAll，后续可以优化搜索功能
        Page<Team> page = teamRepository.findAll(pageable);
        List<TeamDTO> teamDTOs = page.getContent().stream()
                .map(team -> {
                    // 手动加载关联数据避免懒加载问题
                    if (team.getLeader() == null && team.getLeaderId() != null) {
                        User leader = userService.findById(team.getLeaderId());
                        team.setLeader(leader);
                    }

                    if (team.getProject() == null && team.getProjectId() != null) {
                        Project project = projectService.findById(team.getProjectId());
                        team.setProject(project);
                    }

                    // 计算成员数量
                    Integer memberCount = teamMemberRepository.countByTeamId(team.getId());
                    team.setCurrentMembers(memberCount);

                    return TeamDTO.fromTeam(team);
                })
                .collect(Collectors.toList());

        return PageResult.of(teamDTOs, page.getTotalElements(),
                           page.getNumber() + 1, page.getSize());
    }

    @Override
    @Transactional
    public void adminUpdateTeamStatus(Long teamId, Team.TeamStatus status) {
        log.info("管理员更新团队状态 - 团队ID: {}, 新状态: {}", teamId, status);

        Team team = findById(teamId);
        team.setStatus(status);
        teamRepository.save(team);

        log.info("团队状态更新成功 - 团队: {}, 状态: {}", team.getName(), status);
    }

    @Override
    @Transactional
    public void adminDisbandTeam(Long teamId) {
        log.info("管理员解散团队 - 团队ID: {}", teamId);

        Team team = findById(teamId);

        // 清除项目关联
        if (team.getProjectId() != null) {
            team.setProject(null);
            team.setProjectId(null);
        }

        // 删除所有团队成员
        teamMemberRepository.deleteByTeamId(teamId);

        // 删除团队
        teamRepository.delete(team);

        log.info("团队解散成功 - 团队: {}", team.getName());
    }
}
