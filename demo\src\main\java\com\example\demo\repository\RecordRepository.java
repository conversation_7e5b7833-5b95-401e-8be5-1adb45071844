package com.example.demo.repository;

import com.example.demo.entity.Record;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 记录数据访问层
 */
@Repository
public interface RecordRepository extends JpaRepository<Record, Long> {
    
    /**
     * 根据类型查找记录
     */
    List<Record> findByType(Record.RecordType type);
    
    /**
     * 根据类型分页查找记录
     */
    Page<Record> findByType(Record.RecordType type, Pageable pageable);
    
    /**
     * 根据项目ID查找记录
     */
    @Query("SELECT r FROM Record r WHERE r.projectId = :projectId ORDER BY r.createTime DESC")
    Page<Record> findByProjectId(@Param("projectId") Long projectId, Pageable pageable);

    /**
     * 根据团队ID查找记录
     */
    @Query("SELECT r FROM Record r WHERE r.teamId = :teamId ORDER BY r.createTime DESC")
    Page<Record> findByTeamId(@Param("teamId") Long teamId, Pageable pageable);

    /**
     * 根据用户ID查找记录
     */
    @Query("SELECT r FROM Record r WHERE r.user.id = :userId ORDER BY r.createTime DESC")
    Page<Record> findByUserId(@Param("userId") Long userId, Pageable pageable);

    /**
     * 根据项目ID和类型查找记录
     */
    @Query("SELECT r FROM Record r WHERE r.projectId = :projectId AND r.type = :type ORDER BY r.createTime DESC")
    Page<Record> findByProjectIdAndType(@Param("projectId") Long projectId,
                                       @Param("type") Record.RecordType type,
                                       Pageable pageable);

    /**
     * 根据团队ID和类型查找记录
     */
    @Query("SELECT r FROM Record r WHERE r.teamId = :teamId AND r.type = :type ORDER BY r.createTime DESC")
    Page<Record> findByTeamIdAndType(@Param("teamId") Long teamId,
                                    @Param("type") Record.RecordType type,
                                    Pageable pageable);

    /**
     * 根据团队ID和类型查找所有记录（不分页）
     */
    @Query("SELECT r FROM Record r WHERE r.teamId = :teamId AND r.type = :type ORDER BY r.createTime DESC")
    List<Record> findAllByTeamIdAndType(@Param("teamId") Long teamId,
                                       @Param("type") Record.RecordType type);

    /**
     * 根据用户ID和类型查找记录
     */
    @Query("SELECT r FROM Record r WHERE r.user.id = :userId AND r.type = :type ORDER BY r.createTime DESC")
    Page<Record> findByUserIdAndType(@Param("userId") Long userId,
                                    @Param("type") Record.RecordType type,
                                    Pageable pageable);

    /**
     * 根据父记录ID查找回复（按时间排序）
     */
    List<Record> findByParentIdOrderByCreateTimeAsc(Long parentId);

    /**
     * 删除回复记录
     */
    @Modifying
    @Query("DELETE FROM Record r WHERE r.parentId = :parentId")
    void deleteByParentId(@Param("parentId") Long parentId);
    
    /**
     * 查找项目任务
     */
    @Query("SELECT r FROM Record r WHERE r.project.id = :projectId AND r.type = 'TASK' " +
           "ORDER BY r.dueDate ASC, r.priority DESC")
    Page<Record> findProjectTasks(@Param("projectId") Long projectId, Pageable pageable);

    /**
     * 查找团队任务
     */
    @Query("SELECT r FROM Record r WHERE r.teamId = :teamId AND r.type = 'TASK' " +
           "ORDER BY r.dueDate ASC, r.priority DESC")
    Page<Record> findTeamTasks(@Param("teamId") Long teamId, Pageable pageable);

    /**
     * 查找用户任务
     */
    @Query("SELECT r FROM Record r WHERE r.user.id = :userId AND r.type = 'TASK' " +
           "ORDER BY r.dueDate ASC, r.priority DESC")
    Page<Record> findUserTasks(@Param("userId") Long userId, Pageable pageable);

    /**
     * 查找项目讨论（不包括回复）
     */
    @Query("SELECT r FROM Record r WHERE r.project.id = :projectId AND r.type = 'DISCUSSION' " +
           "AND r.parentId IS NULL ORDER BY r.createTime DESC")
    Page<Record> findProjectDiscussions(@Param("projectId") Long projectId, Pageable pageable);

    /**
     * 查找团队讨论（不包括回复）
     */
    @Query("SELECT r FROM Record r WHERE r.teamId = :teamId AND r.type = 'DISCUSSION' " +
           "AND r.parentId IS NULL ORDER BY r.createTime DESC")
    Page<Record> findTeamDiscussions(@Param("teamId") Long teamId, Pageable pageable);

    /**
     * 查找团队讨论相关记录（包括讨论、公告、反馈，不包括任务和提交）
     */
    @Query("SELECT r FROM Record r WHERE r.teamId = :teamId " +
           "AND r.type IN ('DISCUSSION', 'ANNOUNCEMENT', 'FEEDBACK') " +
           "AND r.parentId IS NULL ORDER BY r.createTime DESC")
    Page<Record> findTeamDiscussionRelated(@Param("teamId") Long teamId, Pageable pageable);

    /**
     * 查找项目提交
     */
    @Query("SELECT r FROM Record r WHERE r.project.id = :projectId AND r.type = 'SUBMISSION' " +
           "ORDER BY r.createTime DESC")
    Page<Record> findProjectSubmissions(@Param("projectId") Long projectId, Pageable pageable);

    /**
     * 查找团队提交
     */
    @Query("SELECT r FROM Record r WHERE r.teamId = :teamId AND r.type = 'SUBMISSION' " +
           "ORDER BY r.createTime DESC")
    Page<Record> findTeamSubmissions(@Param("teamId") Long teamId, Pageable pageable);

    /**
     * 查找项目评价
     */
    @Query("SELECT r FROM Record r WHERE r.project.id = :projectId AND r.type = 'EVALUATION_ITEM' " +
           "ORDER BY r.createTime DESC")
    Page<Record> findProjectEvaluations(@Param("projectId") Long projectId, Pageable pageable);
    
    /**
     * 查找即将到期的任务
     */
    @Query("SELECT r FROM Record r WHERE r.type = 'TASK' AND r.dueDate BETWEEN :startTime AND :endTime")
    List<Record> findTasksDueSoon(@Param("startTime") LocalDateTime startTime, 
                                 @Param("endTime") LocalDateTime endTime);
    
    /**
     * 查找逾期任务
     */
    @Query("SELECT r FROM Record r WHERE r.type = 'TASK' AND " +
           "r.dueDate < :currentTime " +
           "ORDER BY r.dueDate ASC")
    List<Record> findOverdueTasks(@Param("currentTime") LocalDateTime currentTime);

    /**
     * 搜索记录
     */
    @Query("SELECT r FROM Record r WHERE " +
           "(:keyword IS NULL OR r.title LIKE %:keyword% OR r.content LIKE %:keyword%) AND " +
           "(:type IS NULL OR r.type = :type) " +
           "ORDER BY r.createTime DESC")
    Page<Record> searchRecords(@Param("keyword") String keyword,
                              @Param("type") Record.RecordType type,
                              Pageable pageable);
    
    /**
     * 统计各类型记录数量
     */
    @Query("SELECT r.type, COUNT(r) FROM Record r GROUP BY r.type")
    List<Object[]> countRecordsByType();
    
    /**
     * 统计用户记录数量
     */
    @Query("SELECT " +
           "COUNT(CASE WHEN r.type = 'TASK' THEN 1 END) as taskCount, " +
           "COUNT(CASE WHEN r.type = 'DISCUSSION' THEN 1 END) as discussionCount, " +
           "COUNT(CASE WHEN r.type = 'SUBMISSION' THEN 1 END) as submissionCount, " +
           "COUNT(CASE WHEN r.type = 'EVALUATION_ITEM' THEN 1 END) as evaluationCount, " +
           "COUNT(CASE WHEN r.type = 'ANNOUNCEMENT' THEN 1 END) as announcementCount " +
           "FROM Record r WHERE r.user.id = :userId")
    Object[] getUserRecordStats(@Param("userId") Long userId);

    /**
     * 统计项目记录数量
     */
    @Query("SELECT " +
           "COUNT(CASE WHEN r.type = 'TASK' THEN 1 END) as taskCount, " +
           "COUNT(CASE WHEN r.type = 'DISCUSSION' THEN 1 END) as discussionCount, " +
           "COUNT(CASE WHEN r.type = 'SUBMISSION' THEN 1 END) as submissionCount, " +
           "COUNT(CASE WHEN r.type = 'EVALUATION_ITEM' THEN 1 END) as evaluationCount, " +
           "COUNT(CASE WHEN r.type = 'ANNOUNCEMENT' THEN 1 END) as announcementCount " +
           "FROM Record r WHERE r.project.id = :projectId")
    Object[] getProjectRecordStats(@Param("projectId") Long projectId);

    /**
     * 统计团队记录数量
     */
    @Query("SELECT " +
           "COUNT(CASE WHEN r.type = 'TASK' THEN 1 END) as taskCount, " +
           "COUNT(CASE WHEN r.type = 'DISCUSSION' THEN 1 END) as discussionCount, " +
           "COUNT(CASE WHEN r.type = 'SUBMISSION' THEN 1 END) as submissionCount, " +
           "COUNT(CASE WHEN r.type = 'EVALUATION_ITEM' THEN 1 END) as evaluationCount " +
           "FROM Record r WHERE r.teamId = :teamId")
    Object[] getTeamRecordStats(@Param("teamId") Long teamId);

    /**
     * 统计指定类型的记录数量
     */
    long countByType(Record.RecordType type);

    /**
     * 统计指定类型和状态的记录数量
     */
    long countByTypeAndStatus(Record.RecordType type, Record.RecordStatus status);

    /**
     * 统计指定类型和项目列表的记录数量
     */
    long countByTypeAndProjectIdIn(Record.RecordType type, List<Long> projectIds);

    /**
     * 统计指定类型、状态和项目列表的记录数量
     */
    long countByTypeAndStatusAndProjectIdIn(Record.RecordType type, Record.RecordStatus status, List<Long> projectIds);

    /**
     * 统计指定类型和用户的记录数量
     */
    @Query("SELECT COUNT(r) FROM Record r WHERE r.type = :type AND r.userId = :userId")
    long countByTypeAndUserId(@Param("type") Record.RecordType type, @Param("userId") Long userId);

    /**
     * 统计指定类型、用户和项目的记录数量
     */
    long countByTypeAndUserIdAndProjectId(Record.RecordType type, Long userId, Long projectId);

    /**
     * 统计指定类型和项目的记录数量
     */
    long countByTypeAndProjectId(Record.RecordType type, Long projectId);

    /**
     * 统计指定类型和团队的记录数量
     */
    @Query("SELECT COUNT(r) FROM Record r WHERE r.type = :type AND r.teamId = :teamId")
    long countByTypeAndTeamId(@Param("type") Record.RecordType type, @Param("teamId") Long teamId);

    /**
     * 统计指定类型、状态和项目的记录数量
     */
    @Query("SELECT COUNT(r) FROM Record r WHERE r.type = :type AND r.status = :status AND r.projectId = :projectId")
    long countByTypeAndStatusAndProjectId(@Param("type") Record.RecordType type,
                                         @Param("status") Record.RecordStatus status,
                                         @Param("projectId") Long projectId);

    /**
     * 统计指定类型、状态和团队的记录数量
     */
    @Query("SELECT COUNT(r) FROM Record r WHERE r.type = :type AND r.status = :status AND r.teamId = :teamId")
    long countByTypeAndStatusAndTeamId(@Param("type") Record.RecordType type,
                                      @Param("status") Record.RecordStatus status,
                                      @Param("teamId") Long teamId);

    /**
     * 统计指定类型、状态、用户和项目的记录数量
     */
    @Query("SELECT COUNT(r) FROM Record r WHERE r.type = :type AND r.status = :status AND r.userId = :userId AND r.projectId = :projectId")
    long countByTypeAndStatusAndUserIdAndProjectId(@Param("type") Record.RecordType type,
                                                  @Param("status") Record.RecordStatus status,
                                                  @Param("userId") Long userId,
                                                  @Param("projectId") Long projectId);

    /**
     * 统计教师待审核的任务提交数量
     */
    @Query("SELECT COUNT(r) FROM Record r WHERE r.type = 'SUBMISSION' AND r.status = 'PENDING' " +
           "AND r.projectId IN (SELECT p.id FROM Project p WHERE p.teacherId = :teacherId)")
    long countPendingTaskSubmissionsByTeacher(@Param("teacherId") Long teacherId);

    /**
     * 获取最近的记录
     */
    List<Record> findTop10ByOrderByCreateTimeDesc();

    /**
     * 根据父记录ID、用户ID和类型查找记录
     */
    Record findByParentIdAndUserIdAndType(Long parentId, Long userId, Record.RecordType type);

    /**
     * 根据项目ID和类型查找所有记录
     */
    @Query("SELECT r FROM Record r WHERE r.projectId = :projectId AND r.type = :type ORDER BY r.createTime DESC")
    List<Record> findAllByProjectIdAndType(@Param("projectId") Long projectId, @Param("type") Record.RecordType type);

    /**
     * 根据用户ID、项目ID和类型查找所有记录
     */
    @Query("SELECT r FROM Record r WHERE r.userId = :userId AND r.projectId = :projectId AND r.type = :type ORDER BY r.createTime DESC")
    List<Record> findAllByUserIdAndProjectIdAndType(@Param("userId") Long userId, @Param("projectId") Long projectId, @Param("type") Record.RecordType type);
}
