package com.example.demo.controller;

import com.example.demo.common.PageResult;
import com.example.demo.common.Result;
import com.example.demo.dto.RecordCreateRequest;
import com.example.demo.dto.RecordDTO;
import com.example.demo.dto.RecordUpdateRequest;
import com.example.demo.entity.Record;
import com.example.demo.entity.FileInfo;
import com.example.demo.repository.RecordRepository;
import com.example.demo.service.RecordService;
import com.example.demo.service.FileInfoService;
import com.example.demo.service.impl.RecordServiceImpl;
import com.example.demo.dto.FileUploadRequest;
import com.example.demo.util.JwtUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import jakarta.servlet.http.HttpServletRequest;
import jakarta.validation.Valid;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 记录管理控制器
 */
@Slf4j
@RestController
@RequestMapping("/records")
@RequiredArgsConstructor
public class RecordController {

    private final RecordService recordService;
    private final RecordRepository recordRepository;
    private final FileInfoService fileInfoService;
    private final JwtUtil jwtUtil;
    
    /**
     * 创建记录
     */
    @PostMapping
    @PreAuthorize("hasRole('TEACHER') or hasRole('STUDENT')")
    public Result<RecordDTO> createRecord(@Valid @RequestBody RecordCreateRequest request,
                                         HttpServletRequest httpRequest) {
        Long userId = jwtUtil.getUserIdFromRequest(httpRequest);
        Record record = recordService.createRecord(userId, request);
        RecordDTO dto = RecordDTO.fromRecord(record);
        return Result.success(dto);
    }
    
    /**
     * 更新记录
     */
    @PutMapping("/{id}")
    @PreAuthorize("hasRole('TEACHER') or hasRole('STUDENT')")
    public Result<RecordDTO> updateRecord(@PathVariable Long id,
                                         @Valid @RequestBody RecordUpdateRequest request,
                                         HttpServletRequest httpRequest) {
        try {
            Long userId = jwtUtil.getUserIdFromRequest(httpRequest);
            log.info("更新记录请求 - ID: {}, 用户: {}, 请求数据: {}", id, userId, request);

            Record record = recordService.updateRecord(id, userId, request);
            RecordDTO dto = RecordDTO.fromRecord(record);

            log.info("记录更新成功 - ID: {}", id);
            return Result.success(dto);
        } catch (Exception e) {
            log.error("更新记录失败 - ID: {}, 错误: {}", id, e.getMessage(), e);
            return Result.error("更新记录失败: " + e.getMessage());
        }
    }
    
    /**
     * 获取记录详情
     */
    @GetMapping("/{id}")
    @PreAuthorize("hasRole('TEACHER') or hasRole('STUDENT')")
    public Result<RecordDTO> getRecord(@PathVariable Long id) {
        RecordDTO dto = recordService.getRecordDTO(id);
        return Result.success(dto);
    }
    
    /**
     * 删除记录
     */
    @DeleteMapping("/{id}")
    @PreAuthorize("hasRole('TEACHER') or hasRole('STUDENT')")
    public Result<Void> deleteRecord(@PathVariable Long id,
                                    HttpServletRequest httpRequest) {
        Long userId = jwtUtil.getUserIdFromRequest(httpRequest);
        recordService.deleteRecord(id, userId);
        return Result.success();
    }
    
    /**
     * 创建任务（仅教师可用）
     */
    @PostMapping("/tasks")
    @PreAuthorize("hasRole('TEACHER')")
    public Result<RecordDTO> createTask(@RequestParam(required = false) Long projectId,
                                       @RequestParam(required = false) Long teamId,
                                       @RequestParam String title,
                                       @RequestParam String content,
                                       @RequestParam(defaultValue = "3") Integer priority,
                                       @RequestParam(required = false) String dueDate,
                                       HttpServletRequest httpRequest) {
        Long userId = jwtUtil.getUserIdFromRequest(httpRequest);
        
        // 解析截止时间
        java.time.LocalDateTime dueDateParsed = null;
        if (dueDate != null && !dueDate.isEmpty()) {
            dueDateParsed = java.time.LocalDateTime.parse(dueDate);
        }
        
        Record record = recordService.createTask(userId, projectId, teamId, title, content, 
                                                priority, dueDateParsed);
        RecordDTO dto = RecordDTO.fromRecord(record);
        return Result.success(dto);
    }

    /**
     * 学生提交任务
     */
    @PostMapping("/tasks/{taskId}/submit")
    @PreAuthorize("hasRole('STUDENT')")
    public Result<RecordDTO> submitTask(@PathVariable Long taskId,
                                       @RequestParam String submissionContent,
                                       @RequestParam(required = false) MultipartFile[] files,
                                       HttpServletRequest httpRequest) {
        Long userId = jwtUtil.getUserIdFromRequest(httpRequest);

        try {
            // 获取原任务信息
            Record originalTask = recordService.findById(taskId);
            if (originalTask == null) {
                return Result.error("任务不存在");
            }

            // 直接更新原任务的状态和内容
            RecordUpdateRequest updateRequest = new RecordUpdateRequest();
            updateRequest.setContent(submissionContent);  // 更新为学生提交的内容
            updateRequest.setStatus(Record.RecordStatus.SUBMITTED);  // 状态改为已提交

            Record record = recordService.updateRecord(taskId, userId, updateRequest);

            // 处理文件上传
            StringBuilder attachmentInfo = new StringBuilder();
            if (files != null && files.length > 0) {
                System.out.println("开始处理 " + files.length + " 个文件");
                for (MultipartFile file : files) {
                    if (!file.isEmpty()) {
                        try {
                            System.out.println("处理文件: " + file.getOriginalFilename() + ", 大小: " + file.getSize());

                            // 创建文件上传请求
                            FileUploadRequest uploadRequest = new FileUploadRequest();
                            uploadRequest.setRecordId(record.getId());  // 关联到提交记录
                            uploadRequest.setProjectId(originalTask.getProjectId());
                            uploadRequest.setTeamId(originalTask.getTeamId());
                            uploadRequest.setDescription("任务提交附件");

                            System.out.println("上传请求: recordId=" + record.getId() +
                                             ", projectId=" + originalTask.getProjectId() +
                                             ", teamId=" + originalTask.getTeamId());

                            // 验证上传请求
                            if (!uploadRequest.isValid()) {
                                System.err.println("上传请求验证失败");
                                continue;
                            }

                            // 上传文件
                            FileInfo fileInfo = fileInfoService.uploadFile(file, uploadRequest, userId);
                            attachmentInfo.append(fileInfo.getOriginalName()).append(";");

                            System.out.println("文件上传成功: " + file.getOriginalFilename() + " -> fileId: " + fileInfo.getId());
                        } catch (Exception e) {
                            System.err.println("文件上传失败: " + file.getOriginalFilename() + " - " + e.getMessage());
                            e.printStackTrace();
                            // 继续处理其他文件，不中断整个提交过程
                        }
                    } else {
                        System.out.println("跳过空文件");
                    }
                }
            } else {
                System.out.println("没有文件需要上传");
            }

            // 更新记录的附件信息
            if (attachmentInfo.length() > 0) {
                // 更新记录的attachments字段
                record.setAttachments(attachmentInfo.toString());
                record = recordRepository.save(record);
                System.out.println("任务提交成功，包含 " + attachmentInfo.toString().split(";").length + " 个附件");
            } else {
                // 确保没有附件时attachments字段为null
                record.setAttachments(null);
                record = recordRepository.save(record);
                System.out.println("任务提交成功，无附件");
            }

            RecordDTO dto = RecordDTO.fromRecord(record);
            return Result.success(dto);
        } catch (Exception e) {
            System.err.println("任务提交失败: " + e.getMessage());
            e.printStackTrace();
            return Result.error("任务提交失败: " + e.getMessage());
        }
    }



    /**
     * 更新任务状态
     */
    @PutMapping("/tasks/{taskId}/status")
    @PreAuthorize("hasRole('STUDENT') or hasRole('TEACHER')")
    public Result<RecordDTO> updateTaskStatus(@PathVariable Long taskId,
                                             @RequestBody Map<String, String> request) {
        try {
            String status = request.get("status");
            System.out.println("更新任务状态请求: taskId=" + taskId + ", status=" + status);

            // 使用RecordServiceImpl的updateTaskStatus方法
            RecordServiceImpl recordServiceImpl = (RecordServiceImpl) recordService;
            Record updatedTask = recordServiceImpl.updateTaskStatus(taskId, status);

            RecordDTO dto = RecordDTO.fromRecord(updatedTask);
            return Result.success(dto);
        } catch (Exception e) {
            System.err.println("更新任务状态失败: taskId=" + taskId + ", error=" + e.getMessage());
            return Result.error("更新任务状态失败: " + e.getMessage());
        }
    }


    /**
     * 简化的任务提交接口（用于调试）
     */
    @PostMapping("/debug/simple-submit")
    @PreAuthorize("hasRole('STUDENT')")
    public Result<RecordDTO> simpleSubmit(@RequestParam("taskId") Long taskId,
                                         @RequestParam("content") String content,
                                         HttpServletRequest httpRequest) {
        try {
            Long userId = jwtUtil.getUserIdFromRequest(httpRequest);
            System.out.println("=== 简化提交调试 ===");
            System.out.println("taskId: " + taskId + ", userId: " + userId);

            // 获取原任务
            Record originalTask = recordService.findById(taskId);
            if (originalTask == null) {
                return Result.error("任务不存在");
            }

            // 创建提交记录
            RecordCreateRequest request = new RecordCreateRequest();
            request.setType(Record.RecordType.SUBMISSION);
            request.setTitle("简化提交 - " + originalTask.getTitle());
            request.setContent(content);
            request.setParentId(taskId);
            request.setProjectId(originalTask.getProjectId());
            request.setTeamId(originalTask.getTeamId());

            Record record = recordService.createRecord(userId, request);
            System.out.println("提交成功，记录ID: " + record.getId());

            RecordDTO dto = RecordDTO.fromRecord(record);
            return Result.success(dto);
        } catch (Exception e) {
            System.err.println("简化提交失败: " + e.getMessage());
            e.printStackTrace();
            return Result.error("提交失败: " + e.getMessage());
        }
    }

    /**
     * 调试接口：获取所有任务数据
     */
    @GetMapping("/debug/tasks")
    public Result<List<Map<String, Object>>> debugTasks() {
        try {
            List<Record> allTasks = recordRepository.findByType(Record.RecordType.TASK);
            List<Map<String, Object>> taskInfo = allTasks.stream().map(task -> {
                Map<String, Object> info = new HashMap<>();
                info.put("id", task.getId());
                info.put("title", task.getTitle());
                info.put("projectId", task.getProjectId());
                info.put("teamId", task.getTeamId());
                info.put("userId", task.getUserId());
                info.put("status", task.getStatus());
                info.put("createTime", task.getCreateTime());
                return info;
            }).collect(Collectors.toList());

            return Result.success(taskInfo);
        } catch (Exception e) {
            System.err.println("获取调试任务信息失败: " + e.getMessage());
            return Result.error("获取调试信息失败: " + e.getMessage());
        }
    }

    /**
     * 教师审核任务提交
     */
    @PostMapping("/tasks/{taskId}/review")
    @PreAuthorize("hasRole('TEACHER')")
    public Result<String> reviewTaskSubmission(@PathVariable Long taskId,
                                              @RequestParam boolean approved,
                                              @RequestParam(required = false) String feedback,
                                              HttpServletRequest httpRequest) {
        Long userId = jwtUtil.getUserIdFromRequest(httpRequest);

        // 更新任务状态
        RecordUpdateRequest request = new RecordUpdateRequest();
        if (approved) {
            request.setStatus(Record.RecordStatus.COMPLETED);
        } else {
            request.setStatus(Record.RecordStatus.IN_PROGRESS);
        }

        if (feedback != null && !feedback.isEmpty()) {
            request.setContent(feedback);
        }

        recordService.updateRecord(taskId, userId, request);

        String message = approved ? "任务审核通过" : "任务需要重新完成";
        return Result.success(message);
    }
    
    /**
     * 创建讨论
     */
    @PostMapping("/discussions")
    @PreAuthorize("hasRole('TEACHER') or hasRole('STUDENT')")
    public Result<RecordDTO> createDiscussion(@RequestParam(required = false) Long projectId,
                                             @RequestParam(required = false) Long teamId,
                                             @RequestParam String title,
                                             @RequestParam String content,
                                             HttpServletRequest httpRequest) {
        Long userId = jwtUtil.getUserIdFromRequest(httpRequest);
        Record record = recordService.createDiscussion(userId, projectId, teamId, title, content);
        RecordDTO dto = RecordDTO.fromRecord(record);
        return Result.success(dto);
    }
    
    /**
     * 回复讨论
     */
    @PostMapping("/discussions/{parentId}/replies")
    @PreAuthorize("hasRole('TEACHER') or hasRole('STUDENT')")
    public Result<RecordDTO> replyDiscussion(@PathVariable Long parentId,
                                            @RequestParam String content,
                                            HttpServletRequest httpRequest) {
        Long userId = jwtUtil.getUserIdFromRequest(httpRequest);
        Record record = recordService.replyDiscussion(userId, parentId, content);
        RecordDTO dto = RecordDTO.fromRecord(record);
        return Result.success(dto);
    }
    
    /**
     * 创建提交
     */
    @PostMapping("/submissions")
    @PreAuthorize("hasRole('STUDENT')")
    public Result<RecordDTO> createSubmission(@RequestParam(required = false) Long projectId,
                                             @RequestParam(required = false) Long teamId,
                                             @RequestParam String title,
                                             @RequestParam String content,
                                             @RequestParam(required = false) String attachments,
                                             HttpServletRequest httpRequest) {
        Long userId = jwtUtil.getUserIdFromRequest(httpRequest);
        Record record = recordService.createSubmission(userId, projectId, teamId, title, content, attachments);
        RecordDTO dto = RecordDTO.fromRecord(record);
        return Result.success(dto);
    }
    
    /**
     * 创建评价
     */
    @PostMapping("/evaluations")
    @PreAuthorize("hasRole('TEACHER')")
    public Result<RecordDTO> createEvaluation(@RequestParam Long projectId,
                                             @RequestParam(required = false) Long teamId,
                                             @RequestParam String title,
                                             @RequestParam String content,
                                             @RequestParam(defaultValue = "3") Integer priority,
                                             HttpServletRequest httpRequest) {
        Long userId = jwtUtil.getUserIdFromRequest(httpRequest);
        Record record = recordService.createEvaluation(userId, projectId, teamId, title, content, priority);
        RecordDTO dto = RecordDTO.fromRecord(record);
        return Result.success(dto);
    }
    
    /**
     * 创建公告
     */
    @PostMapping("/announcements")
    @PreAuthorize("hasRole('TEACHER')")
    public Result<RecordDTO> createAnnouncement(@RequestParam Long projectId,
                                               @RequestParam String title,
                                               @RequestParam String content,
                                               @RequestParam(defaultValue = "3") Integer priority,
                                               HttpServletRequest httpRequest) {
        Long userId = jwtUtil.getUserIdFromRequest(httpRequest);
        Record record = recordService.createAnnouncement(userId, projectId, title, content, priority);
        RecordDTO dto = RecordDTO.fromRecord(record);
        return Result.success(dto);
    }
    

    /**
     * 分页查询所有记录
     */
    @GetMapping
    @PreAuthorize("hasRole('TEACHER') or hasRole('STUDENT')")
    public Result<PageResult<RecordDTO>> getAllRecords(@RequestParam(required = false) Record.RecordType type,
                                                       @RequestParam(defaultValue = "1") int page,
                                                       @RequestParam(defaultValue = "10") int size,
                                                       @RequestParam(defaultValue = "createTime") String sortBy,
                                                       @RequestParam(defaultValue = "desc") String sortDir) {
        Sort sort = Sort.by(Sort.Direction.fromString(sortDir), sortBy);
        Pageable pageable = PageRequest.of(page - 1, size, sort);

        // TODO: 后续可以根据type参数过滤记录类型
        PageResult<RecordDTO> result = recordService.findAllRecords(pageable);

        return Result.success(result);
    }

    /**
     * 分页查询项目记录
     */
    @GetMapping("/projects/{projectId}")
    @PreAuthorize("hasRole('TEACHER') or hasRole('STUDENT')")
    public Result<PageResult<RecordDTO>> getProjectRecords(@PathVariable Long projectId,
                                                           @RequestParam(required = false) Record.RecordType type,
                                                           @RequestParam(defaultValue = "1") int page,
                                                           @RequestParam(defaultValue = "10") int size,
                                                           @RequestParam(defaultValue = "createTime") String sortBy,
                                                           @RequestParam(defaultValue = "desc") String sortDir) {
        Sort sort = Sort.by(Sort.Direction.fromString(sortDir), sortBy);
        Pageable pageable = PageRequest.of(page - 1, size, sort);
        PageResult<RecordDTO> result = recordService.findProjectRecords(projectId, type, pageable);
        return Result.success(result);
    }
    
    /**
     * 分页查询团队记录
     */
    @GetMapping("/teams/{teamId}")
    @PreAuthorize("hasRole('TEACHER') or hasRole('STUDENT')")
    public Result<PageResult<RecordDTO>> getTeamRecords(@PathVariable Long teamId,
                                                        @RequestParam(required = false) Record.RecordType type,
                                                        @RequestParam(defaultValue = "1") int page,
                                                        @RequestParam(defaultValue = "10") int size,
                                                        @RequestParam(defaultValue = "createTime") String sortBy,
                                                        @RequestParam(defaultValue = "desc") String sortDir) {
        Sort sort = Sort.by(Sort.Direction.fromString(sortDir), sortBy);
        Pageable pageable = PageRequest.of(page - 1, size, sort);
        PageResult<RecordDTO> result = recordService.findTeamRecords(teamId, type, pageable);
        return Result.success(result);
    }
    
    /**
     * 分页查询用户记录
     */
    @GetMapping("/users/me")
    @PreAuthorize("hasRole('TEACHER') or hasRole('STUDENT')")
    public Result<PageResult<RecordDTO>> getUserRecords(@RequestParam(required = false) Record.RecordType type,
                                                        @RequestParam(defaultValue = "1") int page,
                                                        @RequestParam(defaultValue = "10") int size,
                                                        @RequestParam(defaultValue = "createTime") String sortBy,
                                                        @RequestParam(defaultValue = "desc") String sortDir,
                                                        HttpServletRequest httpRequest) {
        Long userId = jwtUtil.getUserIdFromRequest(httpRequest);
        Sort sort = Sort.by(Sort.Direction.fromString(sortDir), sortBy);
        Pageable pageable = PageRequest.of(page - 1, size, sort);
        PageResult<RecordDTO> result = recordService.findUserRecords(userId, type, pageable);
        return Result.success(result);
    }
    
    /**
     * 分页查询项目任务
     */
    @GetMapping("/projects/{projectId}/tasks")
    @PreAuthorize("hasRole('TEACHER') or hasRole('STUDENT')")
    public Result<PageResult<RecordDTO>> getProjectTasks(@PathVariable Long projectId,
                                                         @RequestParam(defaultValue = "1") int page,
                                                         @RequestParam(defaultValue = "10") int size,
                                                         @RequestParam(defaultValue = "dueDate") String sortBy,
                                                         @RequestParam(defaultValue = "asc") String sortDir) {
        Sort sort = Sort.by(Sort.Direction.fromString(sortDir), sortBy);
        Pageable pageable = PageRequest.of(page - 1, size, sort);
        PageResult<RecordDTO> result = recordService.findProjectTasks(projectId, pageable);
        return Result.success(result);
    }
    
    /**
     * 分页查询团队任务
     */
    @GetMapping("/teams/{teamId}/tasks")
    @PreAuthorize("hasRole('TEACHER') or hasRole('STUDENT')")
    public Result<PageResult<RecordDTO>> getTeamTasks(@PathVariable Long teamId,
                                                      @RequestParam(defaultValue = "1") int page,
                                                      @RequestParam(defaultValue = "10") int size,
                                                      @RequestParam(defaultValue = "dueDate") String sortBy,
                                                      @RequestParam(defaultValue = "asc") String sortDir) {
        Sort sort = Sort.by(Sort.Direction.fromString(sortDir), sortBy);
        Pageable pageable = PageRequest.of(page - 1, size, sort);
        PageResult<RecordDTO> result = recordService.findTeamTasks(teamId, pageable);
        return Result.success(result);
    }
    
    /**
     * 分页查询用户任务
     */
    @GetMapping("/users/me/tasks")
    @PreAuthorize("hasRole('TEACHER') or hasRole('STUDENT')")
    public Result<PageResult<RecordDTO>> getUserTasks(@RequestParam(defaultValue = "1") int page,
                                                      @RequestParam(defaultValue = "10") int size,
                                                      @RequestParam(defaultValue = "dueDate") String sortBy,
                                                      @RequestParam(defaultValue = "asc") String sortDir,
                                                      HttpServletRequest httpRequest) {
        Long userId = jwtUtil.getUserIdFromRequest(httpRequest);
        Sort sort = Sort.by(Sort.Direction.fromString(sortDir), sortBy);
        Pageable pageable = PageRequest.of(page - 1, size, sort);
        PageResult<RecordDTO> result = recordService.findUserTasks(userId, pageable);
        return Result.success(result);
    }
    
    /**
     * 分页查询项目讨论
     */
    @GetMapping("/projects/{projectId}/discussions")
    @PreAuthorize("hasRole('TEACHER') or hasRole('STUDENT')")
    public Result<PageResult<RecordDTO>> getProjectDiscussions(@PathVariable Long projectId,
                                                               @RequestParam(defaultValue = "1") int page,
                                                               @RequestParam(defaultValue = "10") int size,
                                                               @RequestParam(defaultValue = "createTime") String sortBy,
                                                               @RequestParam(defaultValue = "desc") String sortDir) {
        Sort sort = Sort.by(Sort.Direction.fromString(sortDir), sortBy);
        Pageable pageable = PageRequest.of(page - 1, size, sort);
        PageResult<RecordDTO> result = recordService.findProjectDiscussions(projectId, pageable);
        return Result.success(result);
    }
    
    /**
     * 分页查询团队讨论
     */
    @GetMapping("/teams/{teamId}/discussions")
    @PreAuthorize("hasRole('TEACHER') or hasRole('STUDENT')")
    public Result<PageResult<RecordDTO>> getTeamDiscussions(@PathVariable Long teamId,
                                                            @RequestParam(defaultValue = "1") int page,
                                                            @RequestParam(defaultValue = "10") int size,
                                                            @RequestParam(defaultValue = "createTime") String sortBy,
                                                            @RequestParam(defaultValue = "desc") String sortDir) {
        Sort sort = Sort.by(Sort.Direction.fromString(sortDir), sortBy);
        Pageable pageable = PageRequest.of(page - 1, size, sort);
        PageResult<RecordDTO> result = recordService.findTeamDiscussions(teamId, pageable);
        return Result.success(result);
    }

    /**
     * 分页查询团队讨论相关记录（包括讨论、公告、通知）
     */
    @GetMapping("/teams/{teamId}/discussion-related")
    @PreAuthorize("hasRole('TEACHER') or hasRole('STUDENT')")
    public Result<PageResult<RecordDTO>> getTeamDiscussionRelated(@PathVariable Long teamId,
                                                                  @RequestParam(defaultValue = "1") int page,
                                                                  @RequestParam(defaultValue = "10") int size,
                                                                  @RequestParam(defaultValue = "createTime") String sortBy,
                                                                  @RequestParam(defaultValue = "desc") String sortDir) {
        Sort sort = Sort.by(Sort.Direction.fromString(sortDir), sortBy);
        Pageable pageable = PageRequest.of(page - 1, size, sort);
        PageResult<RecordDTO> result = recordService.findTeamDiscussionRelated(teamId, pageable);
        return Result.success(result);
    }
    
    /**
     * 获取讨论回复
     */
    @GetMapping("/{discussionId}/replies")
    @PreAuthorize("hasRole('TEACHER') or hasRole('STUDENT')")
    public Result<List<RecordDTO>> getDiscussionReplies(@PathVariable Long discussionId) {
        System.out.println("获取讨论回复请求 - 讨论ID: " + discussionId);
        List<RecordDTO> replies = recordService.getDiscussionReplies(discussionId);
        System.out.println("找到 " + replies.size() + " 条回复");
        return Result.success(replies);
    }
    
    /**
     * 搜索记录
     */
    @GetMapping("/search")
    @PreAuthorize("hasRole('TEACHER') or hasRole('STUDENT')")
    public Result<PageResult<RecordDTO>> searchRecords(@RequestParam String keyword,
                                                       @RequestParam(required = false) Record.RecordType type,
                                                       @RequestParam(defaultValue = "1") int page,
                                                       @RequestParam(defaultValue = "10") int size,
                                                       @RequestParam(defaultValue = "createTime") String sortBy,
                                                       @RequestParam(defaultValue = "desc") String sortDir) {
        Sort sort = Sort.by(Sort.Direction.fromString(sortDir), sortBy);
        Pageable pageable = PageRequest.of(page - 1, size, sort);
        PageResult<RecordDTO> result = recordService.searchRecords(keyword, type, pageable);
        return Result.success(result);
    }
    
    /**
     * 获取即将到期的任务
     */
    @GetMapping("/tasks/due-soon")
    @PreAuthorize("hasRole('TEACHER') or hasRole('STUDENT')")
    public Result<List<RecordDTO>> getTasksDueSoon(@RequestParam(defaultValue = "7") int days,
                                                   HttpServletRequest httpRequest) {
        Long userId = jwtUtil.getUserIdFromRequest(httpRequest);
        List<RecordDTO> tasks = recordService.getTasksDueSoon(userId, days);
        return Result.success(tasks);
    }
    
    /**
     * 获取逾期任务
     */
    @GetMapping("/tasks/overdue")
    @PreAuthorize("hasRole('TEACHER') or hasRole('STUDENT')")
    public Result<List<RecordDTO>> getOverdueTasks(HttpServletRequest httpRequest) {
        Long userId = jwtUtil.getUserIdFromRequest(httpRequest);
        List<RecordDTO> tasks = recordService.getOverdueTasks(userId);
        return Result.success(tasks);
    }
    
    /**
     * 标记任务完成
     */
    @PutMapping("/{taskId}/complete")
    @PreAuthorize("hasRole('TEACHER') or hasRole('STUDENT')")
    public Result<Void> markTaskCompleted(@PathVariable Long taskId,
                                         HttpServletRequest httpRequest) {
        Long userId = jwtUtil.getUserIdFromRequest(httpRequest);
        recordService.markTaskCompleted(taskId, userId);
        return Result.success();
    }
    
    /**
     * 标记任务未完成
     */
    @PutMapping("/{taskId}/incomplete")
    @PreAuthorize("hasRole('TEACHER') or hasRole('STUDENT')")
    public Result<Void> markTaskIncomplete(@PathVariable Long taskId,
                                          HttpServletRequest httpRequest) {
        Long userId = jwtUtil.getUserIdFromRequest(httpRequest);
        recordService.markTaskIncomplete(taskId, userId);
        return Result.success();
    }
    
    /**
     * 获取记录统计
     */
    @GetMapping("/stats")
    @PreAuthorize("hasRole('TEACHER') or hasRole('STUDENT')")
    public Result<Object> getRecordStats(HttpServletRequest httpRequest) {
        Long userId = jwtUtil.getUserIdFromRequest(httpRequest);
        Object[] stats = recordService.getUserRecordStats(userId);
        return Result.success(stats);
    }
    
    /**
     * 获取项目记录统计
     */
    @GetMapping("/projects/{projectId}/stats")
    @PreAuthorize("hasRole('TEACHER') or hasRole('STUDENT')")
    public Result<Object> getProjectRecordStats(@PathVariable Long projectId) {
        Object[] stats = recordService.getProjectRecordStats(projectId);
        return Result.success(stats);
    }
    
    /**
     * 获取团队记录统计
     */
    @GetMapping("/teams/{teamId}/stats")
    @PreAuthorize("hasRole('TEACHER') or hasRole('STUDENT')")
    public Result<Object> getTeamRecordStats(@PathVariable Long teamId) {
        Object[] stats = recordService.getTeamRecordStats(teamId);
        return Result.success(stats);
    }

    /**
     * 获取最新活动记录
     */
    @GetMapping("/latest")
    @PreAuthorize("hasRole('TEACHER') or hasRole('STUDENT')")
    public Result<List<RecordDTO>> getLatestRecords(
            @RequestParam(defaultValue = "10") Integer limit,
            HttpServletRequest httpRequest) {
        try {
            Long userId = jwtUtil.getUserIdFromRequest(httpRequest);
            Pageable pageable = PageRequest.of(0, limit, Sort.by(Sort.Direction.DESC, "createTime"));
            PageResult<RecordDTO> result = recordService.findUserRecords(userId, null, pageable);
            return Result.success("获取最新活动成功", result.getRecords());
        } catch (Exception e) {
            return Result.error("获取最新活动失败: " + e.getMessage());
        }
    }
}
