<template>
  <div class="discussion">
    <el-card>
      <template #header>
        <div class="card-header">
          <h3>项目讨论</h3>
          <div class="header-actions">
            <el-select
              v-if="isTeacher"
              v-model="currentTeamId"
              placeholder="选择团队"
              @change="loadDiscussions"
            >
              <el-option
                v-for="team in myTeams"
                :key="team.id"
                :label="`${team.name} (${team.projectName || '未知项目'})`"
                :value="team.id"
              />
            </el-select>
            <div v-else-if="currentTeam" class="current-team-info">
              <span class="team-name">{{ currentTeam.name }}</span>
              <el-tag size="small" type="info">我的团队</el-tag>
            </div>
            <div v-else class="no-team-info">
              <el-tag size="small" type="warning">未加入团队</el-tag>
            </div>

            <el-button
              v-if="currentTeamId"
              type="primary"
              @click="showCreateDialog = true"
              :icon="Plus"
            >
              发起讨论
            </el-button>
            <el-button @click="loadDiscussions" :icon="Refresh">
              刷新
            </el-button>
          </div>
        </div>
      </template>
      
      <!-- 讨论列表 -->
      <div class="discussion-list" v-loading="loading">
        <div v-if="discussions.length === 0 && !loading" class="empty-state">
          <el-empty description="暂无讨论记录">
            <el-button type="primary" @click="showCreateDialog = true">
              发起讨论
            </el-button>
          </el-empty>
        </div>
        
        <div v-else class="discussion-grid">
          <div v-for="discussion in discussions" :key="discussion.id" class="discussion-card">
            <el-card shadow="hover" @click="viewDiscussionDetail(discussion)">
              <h4>{{ discussion.title || '无标题讨论' }}</h4>
              <p class="discussion-description">{{ discussion.content }}</p>

              <div class="discussion-meta">
                <el-tag v-if="discussion.type" :type="getTypeColor(discussion.type)" size="small">
                  {{ getTypeText(discussion.type) }}
                </el-tag>
                <span class="discussion-author">{{ discussion.userName }}</span>
              </div>

              <div class="discussion-info">
                <div class="info-item">
                  <el-icon><Clock /></el-icon>
                  <span>{{ formatDate(discussion.createTime) }}</span>
                </div>
                <div class="info-item" v-if="discussion.attachments && discussion.attachments.length > 0">
                  <el-icon><Document /></el-icon>
                  <span>{{ discussion.attachments.length }}个附件</span>
                </div>
              </div>

              <div class="discussion-footer">
                <el-button size="small" @click.stop="viewDiscussionDetail(discussion)">
                  查看详情
                </el-button>
                <el-button size="small" @click.stop="replyToDiscussion(discussion)" :icon="ChatDotRound">
                  回复
                </el-button>
                <el-button v-if="canEdit(discussion)" size="small" @click.stop="editDiscussion(discussion)" :icon="Edit">
                  编辑
                </el-button>
                <el-button v-if="canDelete(discussion)" size="small" type="danger" @click.stop="deleteDiscussion(discussion.id)" :icon="Delete">
                  删除
                </el-button>
              </div>
            </el-card>
          </div>
        </div>
      </div>
      
      <!-- 分页 -->
      <div v-if="total > 0" class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :total="total"
          :page-sizes="[10, 20, 50]"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="loadDiscussions"
          @current-change="loadDiscussions"
        />
      </div>
    </el-card>
    
    <!-- 创建/编辑讨论对话框 -->
    <el-dialog
      v-model="showCreateDialog"
      :title="editingDiscussion ? '编辑讨论' : '发起讨论'"
      width="600px"
    >
      <el-form
        ref="formRef"
        :model="discussionForm"
        :rules="formRules"
        label-width="80px"
      >
        <el-form-item label="讨论类型" prop="type">
          <el-select v-model="discussionForm.type" placeholder="请选择讨论类型">
            <el-option label="一般讨论" value="DISCUSSION" />
            <el-option label="公告通知" value="ANNOUNCEMENT" />
            <el-option label="反馈建议" value="FEEDBACK" />
            <el-option label="作业提交" value="SUBMISSION" />
            <el-option label="评价项目" value="EVALUATION_ITEM" />
          </el-select>
        </el-form-item>
        
        <el-form-item label="讨论标题" prop="title">
          <el-input v-model="discussionForm.title" placeholder="请输入讨论标题" />
        </el-form-item>
        
        <el-form-item label="讨论内容" prop="content">
          <el-input
            v-model="discussionForm.content"
            type="textarea"
            :rows="6"
            placeholder="请输入讨论内容"
          />
        </el-form-item>
      </el-form>
      
      <template #footer>
        <el-button @click="showCreateDialog = false">取消</el-button>
        <el-button type="primary" @click="submitDiscussion" :loading="submitting">
          {{ editingDiscussion ? '更新' : '发布' }}
        </el-button>
      </template>
    </el-dialog>

    <!-- 回复对话框 -->
    <el-dialog
      v-model="showReplyDialog"
      title="回复讨论"
      width="600px"
      :close-on-click-modal="false"
    >
      <div v-if="replyingToDiscussion" class="reply-context">
        <h4>回复：{{ replyingToDiscussion.title }}</h4>
        <div class="original-content">
          <p><strong>{{ replyingToDiscussion.userName }}</strong> 说：</p>
          <p>{{ replyingToDiscussion.content }}</p>
        </div>
      </div>

      <el-form
        ref="replyFormRef"
        :model="replyForm"
        :rules="replyRules"
        label-width="80px"
        style="margin-top: 20px;"
      >
        <el-form-item label="回复内容" prop="content">
          <el-input
            v-model="replyForm.content"
            type="textarea"
            :rows="6"
            placeholder="请输入您的回复..."
            maxlength="1000"
            show-word-limit
          />
        </el-form-item>
      </el-form>

      <template #footer>
        <el-button @click="showReplyDialog = false">取消</el-button>
        <el-button type="primary" @click="submitReply" :loading="submitting">
          发布回复
        </el-button>
      </template>
    </el-dialog>

    <!-- 讨论详情对话框 -->
    <el-dialog
      v-model="showDetailDialog"
      title="讨论详情"
      width="800px"
      :close-on-click-modal="false"
    >
      <div v-if="currentDiscussion" class="discussion-detail">
        <!-- 主讨论内容 -->
        <div class="main-discussion">
          <div class="discussion-header">
            <div class="header-left">
              <h2 class="discussion-title">{{ currentDiscussion.title }}</h2>
              <div class="discussion-tags">
                <el-tag :type="getTypeColor(currentDiscussion.type)" size="small">
                  {{ getTypeText(currentDiscussion.type) }}
                </el-tag>
              </div>
            </div>
            <div class="header-right">
              <div class="author-info">
                <el-avatar :size="40" class="author-avatar" :src="getAvatarUrl(currentDiscussion.userAvatar)">
                  {{ getInitial(currentDiscussion.userName) }}
                </el-avatar>
                <div class="author-details">
                  <div class="author-name">{{ currentDiscussion.userName }}</div>
                  <div class="publish-time">{{ formatDate(currentDiscussion.createTime) }}</div>
                </div>
              </div>
            </div>
          </div>

          <div class="discussion-content">
            <div class="content-text">{{ currentDiscussion.content }}</div>

            <div v-if="currentDiscussion.attachments && currentDiscussion.attachments.length > 0" class="attachments-section">
              <div class="attachments-title">
                <el-icon><Document /></el-icon>
                <span>附件 ({{ currentDiscussion.attachments.length }})</span>
              </div>
              <div class="attachments-list">
                <div v-for="file in currentDiscussion.attachments" :key="file.id" class="attachment-item">
                  <el-icon><Document /></el-icon>
                  <span class="file-name">{{ file.name }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 回复列表 -->
        <div class="replies-section">
          <div class="replies-header">
            <div class="replies-title">
              <el-icon><ChatDotRound /></el-icon>
              <span>回复讨论 ({{ discussionReplies.length }})</span>
            </div>
            <el-divider />
          </div>

          <div v-if="loadingReplies" class="loading-state">
            <el-icon class="is-loading"><Loading /></el-icon>
            <span>加载回复中...</span>
          </div>

          <div v-else-if="discussionReplies.length === 0" class="empty-state">
            <el-empty description="暂无回复" :image-size="80">
              <el-button type="primary" @click="replyToDiscussion(currentDiscussion)">
                发表回复
              </el-button>
            </el-empty>
          </div>

          <div v-else class="replies-list">
            <div v-for="(reply, index) in discussionReplies" :key="reply.id" class="reply-item">
              <div class="reply-number">#{{ index + 1 }}</div>
              <div class="reply-content-wrapper">
                <div class="reply-header">
                  <div class="reply-author">
                    <el-avatar :size="32" class="reply-avatar" :src="getAvatarUrl(reply.userAvatar)">
                      {{ getInitial(reply.userName) }}
                    </el-avatar>
                    <div class="author-info">
                      <span class="author-name">{{ reply.userName }}</span>
                      <el-tag :type="reply.userRole === 'TEACHER' ? 'warning' : 'info'" size="small" class="role-tag">
                        {{ reply.userRole === 'TEACHER' ? '教师' : '学生' }}
                      </el-tag>
                    </div>
                  </div>
                  <div class="reply-time">
                    {{ formatDate(reply.createTime) }}
                  </div>
                </div>
                <div class="reply-content">
                  {{ reply.content }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <template #footer>
        <el-button @click="showDetailDialog = false">关闭</el-button>
        <el-button type="primary" @click="replyToDiscussion(currentDiscussion)">
          回复
        </el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import { ref, reactive, onMounted, computed } from 'vue'
import { useStore } from 'vuex'
import { recordAPI, teamAPI, projectAPI } from '@/api'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getAvatarUrl, getInitial } from '@/utils/avatar'
import {
  Document,
  ChatDotRound,
  Edit,
  Delete,
  Plus,
  Loading,
  Refresh,
  Clock
} from '@element-plus/icons-vue'

export default {
  name: 'DiscussionView',
  components: {
    Document,
    ChatDotRound,
    Edit,
    Delete,
    Plus,
    Loading,
    Refresh,
    Clock
  },
  setup() {
    const store = useStore()
    const formRef = ref()
    const replyFormRef = ref()
    
    const loading = ref(false)
    const submitting = ref(false)
    const showCreateDialog = ref(false)
    const showDetailDialog = ref(false)
    const showReplyDialog = ref(false)
    const editingDiscussion = ref(null)
    const currentDiscussion = ref(null)
    const replyingToDiscussion = ref(null)
    const discussionReplies = ref([])
    const loadingReplies = ref(false)
    
    const discussions = ref([])
    const myTeams = ref([])
    const currentTeam = ref(null)
    const currentTeamId = ref(null)
    const currentPage = ref(1)
    const pageSize = ref(10)
    const total = ref(0)
    
    const discussionForm = reactive({
      type: 'DISCUSSION',
      title: '',
      content: ''
    })

    const replyForm = reactive({
      content: ''
    })
    
    const formRules = {
      type: [
        { required: true, message: '请选择讨论类型', trigger: 'change' }
      ],
      title: [
        { required: true, message: '请输入讨论标题', trigger: 'blur' },
        { min: 2, max: 100, message: '标题长度在 2 到 100 个字符', trigger: 'blur' }
      ],
      content: [
        { required: true, message: '请输入讨论内容', trigger: 'blur' },
        { min: 10, max: 2000, message: '内容长度在 10 到 2000 个字符', trigger: 'blur' }
      ]
    }

    const replyRules = {
      content: [
        { required: true, message: '请输入回复内容', trigger: 'blur' },
        { min: 5, max: 1000, message: '回复内容长度在 5 到 1000 个字符', trigger: 'blur' }
      ]
    }
    
    // 使用与DashboardView相同的方式获取用户信息
    const currentUser = computed(() => store.getters.currentUser)
    const isTeacher = computed(() => store.getters.isTeacher)
    const isStudent = computed(() => store.getters.isStudent)

    // 加载我的团队
    const loadMyTeams = async () => {
      try {
        if (isTeacher.value) {
          // 首先获取教师发布的项目
          const projectsResponse = await projectAPI.getMyProjects()
          const myProjects = projectsResponse?.records || []

          if (myProjects.length > 0) {
            // 获取所有项目相关的团队
            const allTeams = []

            for (const project of myProjects) {
              try {
                const teamsResponse = await teamAPI.getProjectTeams(project.id)

                if (teamsResponse?.records) {
                  // 为每个团队添加项目信息
                  const teamsWithProject = teamsResponse.records.map(team => ({
                    ...team,
                    projectId: project.id,
                    projectName: project.name
                  }))
                  allTeams.push(...teamsWithProject)
                }
              } catch (err) {
                console.warn(`获取项目 ${project.id} 的团队失败:`, err)
              }
            }

            if (allTeams.length > 0) {
              myTeams.value = allTeams
              currentTeamId.value = allTeams[0].id
              await loadDiscussions()
            } else {
              ElMessage.info('暂无团队申请您发布的项目')
              myTeams.value = []
            }
          } else {
            ElMessage.info('您还没有发布任何项目，无法查看团队讨论')
            myTeams.value = []
          }
        } else {
          // 使用真实API获取学生的团队
          const response = await teamAPI.getMyTeam()

          if (response) {
            currentTeam.value = response
            currentTeamId.value = response.id
            myTeams.value = [response]  // 学生只有一个团队
            await loadDiscussions()
          } else {
            ElMessage.warning('您还没有加入任何团队')
            myTeams.value = []
          }
        }
      } catch (error) {
        console.error('加载团队信息失败:', error)
        ElMessage.error(`加载团队信息失败: ${error.message || '未知错误'}`)
        myTeams.value = []
      }
    }

    // 加载讨论列表
    const loadDiscussions = async () => {
      if (!currentTeamId.value) {
        return
      }

      try {
        loading.value = true

        // 使用专门的团队讨论相关记录API，包括除任务外的所有类型
        const params = {
          page: currentPage.value,
          size: pageSize.value,
          sortBy: 'createTime',
          sortDir: 'desc'
        }

        const response = await recordAPI.getTeamDiscussionRelated(currentTeamId.value, params)
        console.log('团队讨论相关记录原始响应:', response)

        if (response && response.records) {
          console.log('记录数据示例:', response.records[0])

          // 不需要前端过滤，后端已经返回了除任务外的所有记录类型
          const filteredRecords = response.records

          // 处理用户信息显示
          const recordsWithUserInfo = filteredRecords.map(record => {
            // 从creator对象中获取用户信息
            let userName = '未知用户'
            let userRole = 'STUDENT'
            let userId = null

            if (record.creator) {
              userName = record.creator.realName || record.creator.username || '未知用户'
              userRole = record.creator.role || 'STUDENT'
              userId = record.creator.id
            }

            console.log('处理记录用户信息:', {
              recordId: record.id,
              creator: record.creator,
              最终结果: {
                userName: userName,
                userRole: userRole,
                userId: userId
              }
            })

            return {
              ...record,
              userName: userName,
              userRole: userRole,
              userId: userId,
              userAvatar: record.creator?.avatar || null
            }
          })

          discussions.value = recordsWithUserInfo.map(record => ({
            id: record.id,
            title: record.title || '无标题',
            content: record.content || '',
            type: record.type || 'DISCUSSION',
            userName: record.userName,
            userRole: record.userRole,
            userId: record.userId,
            userAvatar: record.userAvatar, // 确保头像数据被传递
            createTime: record.createTime,
            updateTime: record.updateTime,
            attachments: record.attachments || []
          }))

          // 使用后端返回的准确总数（后端已经过滤了正确的记录类型）
          total.value = response.total || 0
        } else {
          discussions.value = []
          total.value = 0
        }

        loading.value = false

      } catch (error) {
        console.error('加载讨论列表失败:', error)
        ElMessage.error('加载讨论列表失败')
        loading.value = false
        discussions.value = []
        total.value = 0
      }
    }
    
    // 提交讨论
    const submitDiscussion = async () => {
      if (!formRef.value) return

      // 检查必要的数据
      if (!currentTeamId.value) {
        ElMessage.error('请先选择团队')
        return
      }

      try {
        await formRef.value.validate()
        submitting.value = true

        // 准备提交的数据
        const recordData = {
          title: discussionForm.title,
          content: discussionForm.content,
          type: discussionForm.type, // 直接使用用户选择的类型
          teamId: currentTeamId.value,
          projectId: currentTeam.value?.projectId || null
        }

        console.log('提交讨论数据:', recordData)
        console.log('当前用户信息:', currentUser.value)
        console.log('localStorage token:', localStorage.getItem('token'))

        if (editingDiscussion.value) {
          // 更新现有讨论
          console.log('更新讨论，ID:', editingDiscussion.value.id)
          const response = await recordAPI.updateRecord(editingDiscussion.value.id, recordData)
          console.log('更新讨论响应:', response)
          ElMessage.success('讨论更新成功')
          // 重新加载讨论列表
          await loadDiscussions()
        } else {
          // 创建新讨论
          console.log('创建新讨论')
          const response = await recordAPI.createRecord(recordData)
          console.log('创建讨论响应:', response)
          ElMessage.success('讨论发布成功')
          // 重新加载讨论列表
          await loadDiscussions()
        }

        showCreateDialog.value = false
        resetForm()
        submitting.value = false

      } catch (error) {
        console.error('提交讨论失败:', error)
        ElMessage.error(`提交讨论失败: ${error.response?.data?.message || error.message || '未知错误'}`)
        submitting.value = false
      }
    }
    
    // 重置表单
    const resetForm = () => {
      Object.assign(discussionForm, {
        type: 'DISCUSSION',
        title: '',
        content: ''
      })
      editingDiscussion.value = null
    }
    
    // 编辑讨论
    const editDiscussion = (discussion) => {
      editingDiscussion.value = discussion
      Object.assign(discussionForm, {
        type: discussion.type,
        title: discussion.title,
        content: discussion.content
      })
      showCreateDialog.value = true
    }
    
    // 删除讨论
    const deleteDiscussion = async (id) => {
      try {
        await ElMessageBox.confirm('确定要删除这条讨论吗？', '确认删除', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })

        // 使用真实API删除讨论
        const response = await recordAPI.deleteRecord(id)
        if (response) {
          ElMessage.success('讨论删除成功')
          // 重新加载讨论列表
          await loadDiscussions()
        }

      } catch (error) {
        if (error !== 'cancel') {
          console.error('删除讨论失败:', error)
          ElMessage.error('删除讨论失败')
        }
      }
    }

    // 回复讨论
    const replyToDiscussion = (discussion) => {
      replyingToDiscussion.value = discussion
      replyForm.content = ''
      showReplyDialog.value = true
    }

    // 提交回复
    const submitReply = async () => {
      if (!replyFormRef.value) return

      // 检查必要的数据
      if (!currentTeamId.value) {
        ElMessage.error('请先选择团队')
        return
      }

      if (!replyingToDiscussion.value) {
        ElMessage.error('回复目标不存在')
        return
      }

      try {
        await replyFormRef.value.validate()
      } catch (error) {
        return
      }

      try {
        submitting.value = true

        // 准备回复数据
        const replyData = {
          title: `回复：${replyingToDiscussion.value.title}`,
          content: replyForm.content,
          type: 'DISCUSSION', // 使用DISCUSSION类型而不是REPLY
          parentId: replyingToDiscussion.value.id, // 父讨论ID
          // 不绑定teamId，这样回复只会在项目详情界面显示，不会在项目讨论界面显示
          projectId: currentTeam.value?.projectId || null
        }

        console.log('提交回复数据:', replyData)
        console.log('当前用户信息:', currentUser.value)
        console.log('localStorage token:', localStorage.getItem('token'))

        const response = await recordAPI.createRecord(replyData)
        console.log('回复响应:', response)

        ElMessage.success('回复发布成功')
        showReplyDialog.value = false
        replyForm.content = ''

        // 如果详情对话框是打开的，重新加载回复
        if (showDetailDialog.value && currentDiscussion.value) {
          await loadDiscussionReplies(currentDiscussion.value.id)
        }

        replyingToDiscussion.value = null
        // 重新加载讨论列表
        await loadDiscussions()

        submitting.value = false

      } catch (error) {
        console.error('提交回复失败:', error)
        ElMessage.error(`提交回复失败: ${error.response?.data?.message || error.message || '未知错误'}`)
        submitting.value = false
      }
    }

    // 加载讨论回复
    const loadDiscussionReplies = async (discussionId) => {
      try {
        loadingReplies.value = true
        console.log('开始加载讨论回复，讨论ID:', discussionId)
        console.log('请求URL将是:', `/api/records/${discussionId}/replies`)

        const response = await recordAPI.getDiscussionReplies(discussionId)
        console.log('回复API响应:', response)

        // 检查响应结构
        console.log('响应结构检查:', {
          response: response,
          responseType: typeof response,
          isArray: Array.isArray(response),
          responseLength: Array.isArray(response) ? response.length : 'not array'
        })

        if (response && Array.isArray(response)) {
          // 处理回复数据，确保用户信息正确显示
          discussionReplies.value = response.map(reply => ({
            ...reply,
            userName: reply.creator?.realName || reply.creator?.username || '未知用户',
            userRole: reply.creator?.role || 'STUDENT',
            userId: reply.creator?.id,
            userAvatar: reply.creator?.avatar || null
          }))
          console.log('处理后的回复数据:', discussionReplies.value)
        } else if (response === null || response === undefined) {
          console.log('API返回null/undefined数据，可能没有回复')
          discussionReplies.value = []
        } else {
          console.log('API返回空数据或格式不正确，响应:', response)
          discussionReplies.value = []
        }

        loadingReplies.value = false
      } catch (error) {
        console.error('加载回复失败:', error)
        console.error('错误详情:', {
          message: error.message,
          response: error.response?.data,
          status: error.response?.status,
          url: error.config?.url
        })
        ElMessage.error(`加载回复失败: ${error.response?.data?.message || error.message || '未知错误'}`)
        loadingReplies.value = false
        discussionReplies.value = []
      }
    }

    // 查看讨论详情
    const viewDiscussionDetail = async (discussion) => {
      currentDiscussion.value = discussion
      showDetailDialog.value = true

      // 先清空之前的回复数据
      discussionReplies.value = []

      // 加载该讨论的回复
      console.log('准备加载讨论回复，讨论对象:', {
        id: discussion.id,
        title: discussion.title,
        type: discussion.type,
        parentId: discussion.parentId
      })
      await loadDiscussionReplies(discussion.id)
    }

    // 权限检查
    const canEdit = (discussion) => {
      return discussion.userId === currentUser.value?.id
    }
    
    const canDelete = (discussion) => {
      return discussion.userId === currentUser.value?.id || currentUser.value?.role === 'TEACHER'
    }
    
    // 工具方法
    const formatDate = (date) => {
      if (!date) return ''
      return new Date(date).toLocaleString('zh-CN')
    }
    
    const getTypeColor = (type) => {
      const colorMap = {
        'DISCUSSION': 'primary',
        'ANNOUNCEMENT': 'danger',
        'FEEDBACK': 'info',
        'SUBMISSION': 'success',
        'EVALUATION_ITEM': 'warning',
        'TASK': 'warning'  // 虽然不会在讨论页面显示，但保留以防万一
      }
      return colorMap[type] || 'info'
    }

    const getTypeText = (type) => {
      const textMap = {
        'DISCUSSION': '一般讨论',
        'ANNOUNCEMENT': '公告通知',
        'FEEDBACK': '反馈建议',
        'SUBMISSION': '作业提交',
        'EVALUATION_ITEM': '评价项目',
        'TASK': '任务'  // 虽然不会在讨论页面显示，但保留以防万一
      }
      return textMap[type] || type
    }
    
    onMounted(() => {
      console.log('组件挂载时的用户信息:', {
        currentUser: currentUser.value,
        token: localStorage.getItem('token'),
        storeUser: store.state.user,
        isAuthenticated: store.state.isAuthenticated
      })
      loadMyTeams()
    })
    
    return {
      loading,
      submitting,
      showCreateDialog,
      showDetailDialog,
      showReplyDialog,
      editingDiscussion,
      currentDiscussion,
      replyingToDiscussion,
      discussionReplies,
      loadingReplies,
      discussions,
      myTeams,
      currentTeam,
      currentTeamId,
      currentPage,
      pageSize,
      total,
      discussionForm,
      replyForm,
      formRules,
      replyRules,
      formRef,
      replyFormRef,
      currentUser,
      isTeacher,
      isStudent,
      store,
      loadDiscussions,
      loadDiscussionReplies,
      submitDiscussion,
      submitReply,
      editDiscussion,
      deleteDiscussion,
      replyToDiscussion,
      viewDiscussionDetail,
      canEdit,
      canDelete,
      formatDate,
      getTypeColor,
      getTypeText,
      // 头像工具函数
      getAvatarUrl,
      getInitial
    }
  }
}
</script>

<style scoped>
.discussion {
  padding: 0;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.card-header h3 {
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.current-team-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.team-name {
  font-weight: 500;
  color: #303133;
}

.discussion-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 20px;
  margin-top: 16px;
}

.discussion-card {
  cursor: pointer;
  transition: all 0.3s ease;
  height: 100%;
}

.discussion-card:hover {
  transform: translateY(-4px);
}

.discussion-card .el-card {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.discussion-card .el-card__body {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 200px; /* 设置最小高度，确保卡片一致性 */
}

.discussion-card h4 {
  margin: 0 0 10px 0;
  color: #303133;
  cursor: pointer;
}

.discussion-card h4:hover {
  color: #409eff;
}

.discussion-description {
  color: #606266;
  margin: 10px 0;
  display: -webkit-box;
  -webkit-line-clamp: 2; /* 只显示2行 */
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.5;
  height: 3em; /* 固定高度为2行 (1.5 * 2 = 3em) */
  flex: none; /* 不允许弹性伸缩，保持固定高度 */
}

.discussion-meta {
  display: flex;
  gap: 8px;
  align-items: center;
  margin: 12px 0;
  padding-top: 8px;
  border-top: 1px solid #f0f0f0;
}

.discussion-author {
  color: #909399;
  font-size: 13px;
}

.discussion-info {
  margin: 12px 0;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 6px;
  color: #909399;
  font-size: 13px;
}

.discussion-footer {
  margin-top: auto;
  text-align: right;
}

.pagination {
  margin-top: var(--space-6);
  display: flex;
  justify-content: center;
  align-items: center;
  padding: var(--space-4) 0;
}

/* 讨论详情对话框样式 */
.discussion-detail {
  max-height: 70vh;
  overflow-y: auto;
}

.main-discussion {
  background: #fff;
  border-radius: 8px;
  margin-bottom: 24px;
}

.discussion-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding-bottom: 16px;
  border-bottom: 2px solid #f0f0f0;
  margin-bottom: 20px;
}

.header-left {
  flex: 1;
}

.discussion-title {
  margin: 0 0 12px 0;
  color: #303133;
  font-size: 20px;
  font-weight: 600;
  line-height: 1.4;
}

.discussion-tags {
  display: flex;
  gap: 8px;
}

.header-right {
  margin-left: 20px;
}

.author-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.author-avatar {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  font-weight: 600;
}

.author-details {
  text-align: right;
}

.author-name {
  display: block;
  font-weight: 600;
  color: #303133;
  margin-bottom: 4px;
}

.publish-time {
  font-size: 13px;
  color: #909399;
}

.discussion-content {
  padding: 0;
}

.content-text {
  color: #606266;
  line-height: 1.8;
  font-size: 15px;
  margin-bottom: 20px;
  white-space: pre-wrap;
}

.attachments-section {
  background: #f8f9fa;
  border-radius: 6px;
  padding: 16px;
  border-left: 4px solid #409eff;
}

.attachments-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 12px;
}

.attachments-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.attachment-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: white;
  border-radius: 4px;
  border: 1px solid #e4e7ed;
  transition: all 0.3s ease;
}

.attachment-item:hover {
  border-color: #409eff;
  background: #f0f9ff;
}

.file-name {
  color: #606266;
  font-size: 14px;
}

/* 回复列表样式 */
.replies-section {
  margin-top: 32px;
  padding-top: 24px;
  border-top: 2px solid #f0f0f0;
}

.replies-header {
  margin-bottom: 20px;
}

.replies-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
  margin-bottom: 16px;
}

.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 40px;
  color: #909399;
}

.empty-state {
  text-align: center;
  padding: 40px 20px;
}

.replies-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.reply-item {
  display: flex;
  gap: 12px;
  padding: 16px;
  background: #fafbfc;
  border-radius: 8px;
  border: 1px solid #e4e7ed;
  transition: all 0.3s ease;
}

.reply-item:hover {
  background: #f5f7fa;
  border-color: #c0c4cc;
}

.reply-number {
  color: #909399;
  font-size: 12px;
  font-weight: 600;
  min-width: 24px;
  text-align: center;
  padding-top: 4px;
}

.reply-content-wrapper {
  flex: 1;
}

.reply-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.reply-author {
  display: flex;
  align-items: center;
  gap: 10px;
}

.reply-avatar {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
  color: #303133;
  font-weight: 600;
}

.author-info {
  display: flex;
  align-items: center;
  gap: 8px;
}

.author-name {
  font-weight: 600;
  color: #303133;
  font-size: 14px;
}

.role-tag {
  font-size: 11px;
}

.reply-time {
  font-size: 12px;
  color: #909399;
}

.reply-content {
  color: #606266;
  line-height: 1.6;
  font-size: 14px;
  white-space: pre-wrap;
}
</style>


