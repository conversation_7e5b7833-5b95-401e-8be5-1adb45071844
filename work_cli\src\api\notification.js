import request from '@/utils/request'

export const notificationAPI = {
  // 获取通知列表
  getNotifications(params = {}) {
    return request.get('/notifications', { params })
  },

  // 获取未读通知数量
  getUnreadCount() {
    return request.get('/notifications/unread-count')
  },

  // 标记通知为已读
  markAsRead(notificationId) {
    return request.put(`/notifications/${notificationId}/read`)
  },

  // 批量标记为已读
  markAllAsRead() {
    return request.put('/notifications/mark-all-read')
  },

  // 删除通知
  deleteNotification(notificationId) {
    return request.delete(`/notifications/${notificationId}`)
  },

  // 清空所有通知
  clearAllNotifications() {
    return request.delete('/notifications/clear-all')
  },

  // 获取通知详情
  getNotificationDetail(notificationId) {
    return request.get(`/notifications/${notificationId}`)
  },

  // 创建系统通知 (管理员功能)
  createSystemNotification(data) {
    return request.post('/notifications/system', data)
  },

  // 获取通知设置
  getNotificationSettings() {
    return request.get('/notifications/settings')
  },

  // 更新通知设置
  updateNotificationSettings(settings) {
    return request.put('/notifications/settings', settings)
  },

  // 基于records表的通知相关API
  
  // 获取项目相关通知
  getProjectNotifications(projectId, params = {}) {
    return request.get(`/records/projects/${projectId}/notifications`, { params })
  },

  // 获取团队相关通知
  getTeamNotifications(teamId, params = {}) {
    return request.get(`/records/teams/${teamId}/notifications`, { params })
  },

  // 创建项目通知记录
  createProjectNotification(projectId, data) {
    return request.post(`/records/projects/${projectId}/notifications`, {
      ...data,
      recordType: 'ANNOUNCEMENT',
      targetType: 'PROJECT',
      targetId: projectId
    })
  },

  // 创建团队通知记录
  createTeamNotification(teamId, data) {
    return request.post(`/records/teams/${teamId}/notifications`, {
      ...data,
      recordType: 'ANNOUNCEMENT',
      targetType: 'TEAM',
      targetId: teamId
    })
  },

  // 创建用户通知记录
  createUserNotification(userId, data) {
    return request.post('/records', {
      ...data,
      recordType: 'ANNOUNCEMENT',
      targetType: 'USER',
      targetId: userId
    })
  },

  // 获取用户的所有通知记录
  getUserNotifications(params = {}) {
    return request.get('/records/notifications', { 
      params: {
        ...params,
        recordType: 'ANNOUNCEMENT'
      }
    })
  },

  // 获取系统通知记录
  getSystemNotifications(params = {}) {
    return request.get('/records/system-notifications', { 
      params: {
        ...params,
        recordType: 'ANNOUNCEMENT'
      }
    })
  },

  // 通知类型枚举
  NOTIFICATION_TYPES: {
    SYSTEM: 'SYSTEM',           // 系统通知
    PROJECT: 'PROJECT',         // 项目通知
    TEAM: 'TEAM',              // 团队通知
    EVALUATION: 'EVALUATION',   // 评价通知
    MESSAGE: 'MESSAGE',         // 消息通知
    ASSIGNMENT: 'ASSIGNMENT',   // 分配通知
    DEADLINE: 'DEADLINE',       // 截止日期通知
    APPROVAL: 'APPROVAL'        // 审批通知
  },

  // 通知优先级枚举
  NOTIFICATION_PRIORITIES: {
    LOW: 'LOW',
    NORMAL: 'NORMAL',
    HIGH: 'HIGH',
    URGENT: 'URGENT'
  },

  // 通知状态枚举
  NOTIFICATION_STATUS: {
    UNREAD: 'UNREAD',
    READ: 'READ',
    ARCHIVED: 'ARCHIVED'
  }
}

// 通知工具函数
export const notificationUtils = {
  // 格式化通知时间
  formatNotificationTime(time) {
    const now = new Date()
    const notificationTime = new Date(time)
    const diff = now - notificationTime

    if (diff < 60000) { // 1分钟内
      return '刚刚'
    } else if (diff < 3600000) { // 1小时内
      return `${Math.floor(diff / 60000)}分钟前`
    } else if (diff < 86400000) { // 1天内
      return `${Math.floor(diff / 3600000)}小时前`
    } else if (diff < 604800000) { // 1周内
      return `${Math.floor(diff / 86400000)}天前`
    } else {
      return notificationTime.toLocaleDateString()
    }
  },

  // 获取通知类型标签
  getNotificationTypeLabel(type) {
    const typeMap = {
      SYSTEM: '系统',
      PROJECT: '项目',
      TEAM: '团队',
      EVALUATION: '评价',
      MESSAGE: '消息',
      ASSIGNMENT: '分配',
      DEADLINE: '截止',
      APPROVAL: '审批'
    }
    return typeMap[type] || '其他'
  },

  // 获取通知优先级颜色
  getNotificationPriorityColor(priority) {
    const colorMap = {
      LOW: '#909399',
      NORMAL: '#409EFF',
      HIGH: '#E6A23C',
      URGENT: '#F56C6C'
    }
    return colorMap[priority] || '#909399'
  },

  // 获取通知图标
  getNotificationIcon(type) {
    const iconMap = {
      SYSTEM: 'Setting',
      PROJECT: 'Folder',
      TEAM: 'UserFilled',
      EVALUATION: 'Star',
      MESSAGE: 'ChatDotRound',
      ASSIGNMENT: 'DocumentChecked',
      DEADLINE: 'Clock',
      APPROVAL: 'Check'
    }
    return iconMap[type] || 'Bell'
  },

  // 生成通知内容
  generateNotificationContent(type, data) {
    const templates = {
      PROJECT_CREATED: '新项目"{title}"已创建',
      PROJECT_ASSIGNED: '项目"{title}"已分配给您的团队',
      TEAM_JOINED: '新成员加入了团队"{teamName}"',
      EVALUATION_RECEIVED: '您收到了新的评价',
      DEADLINE_REMINDER: '项目"{title}"即将截止',
      APPROVAL_NEEDED: '有新的申请需要您审核'
    }
    
    return templates[type] || '您有新的通知'
  }
}

export default notificationAPI
